package top.lacrus.kyokuerabu.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import top.lacrus.kyokuerabu.client.JLayerPlayer;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;

public class TestPlayerCommand {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("kyo")
            .then(Commands.literal("test_player")
                .then(Commands.argument("filename", StringArgumentType.string())
                    .executes(TestPlayerCommand::executeTestPlayer)
                )
                .executes(TestPlayerCommand::executeTestPlayerDefault)
            )
        );
    }
    
    private static int executeTestPlayerDefault(CommandContext<CommandSourceStack> context) {
        return executeTestPlayer(context, "fssx.mp3");
    }
    
    private static int executeTestPlayer(CommandContext<CommandSourceStack> context) {
        String fileName = StringArgumentType.getString(context, "filename");
        return executeTestPlayer(context, fileName);
    }
    
    private static int executeTestPlayer(CommandContext<CommandSourceStack> context, String fileName) {
        CommandSourceStack source = context.getSource();
        
        // 检查是否在客户端
        if (!source.getLevel().isClientSide) {
            source.sendFailure(Component.literal("§c此命令只能在客户端执行"));
            return 0;
        }
        
        source.sendSuccess(() -> Component.literal("§e[Kyokuerabu] 开始测试播放器: " + fileName), false);
        
        // 异步执行测试
        CompletableFuture.runAsync(() -> {
            testPlayerOnClient(fileName);
        });
        
        return 1;
    }
    
    @OnlyIn(Dist.CLIENT)
    private static void testPlayerOnClient(String fileName) {
        try {
            // 测试文件路径
            Path testFile = Paths.get("client_music", fileName);
            
            System.out.println("========== 客户端播放器测试 ==========");
            System.out.println("测试文件: " + testFile.toAbsolutePath());
            
            if (!Files.exists(testFile)) {
                System.out.println("测试文件不存在: " + testFile.toAbsolutePath());
                sendClientMessage("§c[Kyokuerabu] 测试文件不存在: " + fileName);
                return;
            }
            
            long fileSize = Files.size(testFile);
            System.out.println("文件大小: " + fileSize + " bytes");
            sendClientMessage("§a[Kyokuerabu] 找到测试文件: " + fileName + " (" + (fileSize / 1024 / 1024) + " MB)");
            
            // 测试1: 使用JLayerPlayer播放
            System.out.println("=== 测试1: JLayerPlayer.playMusicFile ===");
            sendClientMessage("§e[Kyokuerabu] 测试1: 使用JLayerPlayer播放");
            
            JLayerPlayer.playMusicFile(testFile, fileName);
            
            System.out.println("JLayerPlayer.playMusicFile 调用完成");
            sendClientMessage("§a[Kyokuerabu] JLayerPlayer.playMusicFile 调用完成");
            
            // 等待5秒后进行测试2
            Thread.sleep(5000);
            
            // 测试2: 直接使用AdvancedPlayer
            System.out.println("=== 测试2: 直接AdvancedPlayer ===");
            sendClientMessage("§e[Kyokuerabu] 测试2: 直接使用AdvancedPlayer");
            
            testDirectAdvancedPlayer(testFile, fileName);
            
        } catch (Exception e) {
            System.out.println("客户端测试失败: " + e.getMessage());
            e.printStackTrace();
            sendClientMessage("§c[Kyokuerabu] 测试失败: " + e.getMessage());
        }
    }
    
    @OnlyIn(Dist.CLIENT)
    private static void testDirectAdvancedPlayer(Path musicFile, String fileName) {
        try {
            System.out.println("开始直接AdvancedPlayer测试...");
            
            java.io.FileInputStream fis = new java.io.FileInputStream(musicFile.toFile());
            java.io.BufferedInputStream bis = new java.io.BufferedInputStream(fis);
            
            javazoom.jl.player.advanced.AdvancedPlayer player = new javazoom.jl.player.advanced.AdvancedPlayer(bis);
            
            // 添加播放监听器
            player.setPlayBackListener(new javazoom.jl.player.advanced.PlaybackListener() {
                @Override
                public void playbackStarted(javazoom.jl.player.advanced.PlaybackEvent evt) {
                    System.out.println("✓ 直接播放开始事件触发");
                    sendClientMessage("§a[Kyokuerabu] ✓ 直接播放开始");
                }
                
                @Override
                public void playbackFinished(javazoom.jl.player.advanced.PlaybackEvent evt) {
                    System.out.println("✓ 直接播放完成事件触发");
                    sendClientMessage("§e[Kyokuerabu] ✓ 直接播放完成");
                }
            });
            
            System.out.println("直接AdvancedPlayer 创建成功");
            sendClientMessage("§a[Kyokuerabu] 直接AdvancedPlayer创建成功");
            
            // 在新线程中播放
            Thread playThread = new Thread(() -> {
                try {
                    System.out.println("开始直接播放...");
                    sendClientMessage("§a[Kyokuerabu] 开始直接播放...");
                    
                    player.play();
                    
                    System.out.println("直接播放方法调用完成");
                    sendClientMessage("§e[Kyokuerabu] 直接播放方法调用完成");
                    
                } catch (Exception e) {
                    System.out.println("直接播放错误: " + e.getMessage());
                    e.printStackTrace();
                    sendClientMessage("§c[Kyokuerabu] 直接播放错误: " + e.getMessage());
                }
            });
            
            playThread.setDaemon(true);
            playThread.setName("DirectTestPlayer-" + fileName);
            playThread.start();
            
            System.out.println("直接播放线程已启动");
            sendClientMessage("§a[Kyokuerabu] 直接播放线程已启动");
            
        } catch (Exception e) {
            System.out.println("直接AdvancedPlayer测试失败: " + e.getMessage());
            e.printStackTrace();
            sendClientMessage("§c[Kyokuerabu] 直接测试失败: " + e.getMessage());
        }
    }
    
    @OnlyIn(Dist.CLIENT)
    private static void sendClientMessage(String message) {
        try {
            net.minecraft.client.Minecraft.getInstance().execute(() -> {
                if (net.minecraft.client.Minecraft.getInstance().player != null) {
                    net.minecraft.client.Minecraft.getInstance().player.sendSystemMessage(
                        net.minecraft.network.chat.Component.literal(message)
                    );
                }
            });
        } catch (Exception e) {
            System.out.println("发送客户端消息失败: " + e.getMessage());
        }
    }
}
