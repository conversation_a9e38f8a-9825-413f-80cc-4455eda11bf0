package top.lacrus.kyokuerabu.command;

import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.builder.ArgumentBuilder;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import com.mojang.brigadier.suggestion.Suggestions;
import com.mojang.brigadier.suggestion.SuggestionsBuilder;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.SharedSuggestionProvider;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.PacketDistributor;
import top.lacrus.kyokuerabu.network.NetworkHandler;
import top.lacrus.kyokuerabu.network.PlayLocalMusicChunkPacket;
import top.lacrus.kyokuerabu.network.SimpleMusicPacket;
import top.lacrus.kyokuerabu.server.ServerMusicCacheManager;
import top.lacrus.kyokuerabu.util.LocalMusicManager;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public class PlayLocalCommand {
    
    // 音乐文件名建议提供器
    private static final SuggestionProvider<CommandSourceStack> MUSIC_FILE_SUGGESTIONS = 
        (context, builder) -> {
            List<String> musicFiles = LocalMusicManager.getAvailableMusicFiles();
            return SharedSuggestionProvider.suggest(musicFiles, builder);
        };
    
    public static ArgumentBuilder<CommandSourceStack, ?> register() {
        return Commands.literal("play_local")
            .then(Commands.argument("targets", EntityArgument.players())
                .then(Commands.argument("filename", StringArgumentType.greedyString())
                    .suggests(MUSIC_FILE_SUGGESTIONS)
                    .executes(PlayLocalCommand::execute)
                )
            );
    }
    
    private static int execute(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        Collection<ServerPlayer> targets = EntityArgument.getPlayers(context, "targets");
        String fileName = StringArgumentType.getString(context, "filename");
        
        // 验证文件名安全性
        if (!LocalMusicManager.isValidFileName(fileName)) {
            source.sendFailure(Component.literal("§c无效的文件名: " + fileName));
            return 0;
        }
        
        // 检查文件是否存在
        if (!LocalMusicManager.musicFileExists(fileName)) {
            source.sendFailure(Component.literal("§c音乐文件不存在: " + fileName));
            
            // 显示可用的音乐文件
            List<String> availableFiles = LocalMusicManager.getAvailableMusicFiles();
            if (availableFiles.isEmpty()) {
                source.sendFailure(Component.literal("§c音乐目录中没有MP3文件。请将MP3文件放入服务器的 music 目录中。"));
                source.sendFailure(Component.literal("§c音乐目录位置: " + LocalMusicManager.getMusicDirectoryPath()));
            } else {
                source.sendFailure(Component.literal("§c可用的音乐文件:"));
                for (String file : availableFiles) {
                    source.sendFailure(Component.literal("§7- " + file));
                }
            }
            return 0;
        }
        
        // 获取音乐文件路径
        Path musicFilePath = LocalMusicManager.getMusicFilePath(fileName);
        if (!Files.exists(musicFilePath)) {
            source.sendFailure(Component.literal("§c音乐文件不存在: " + fileName));
            return 0;
        }

        // 检查文件大小（限制为50MB）
        try {
            long fileSize = Files.size(musicFilePath);
            if (fileSize > 50 * 1024 * 1024) {
                source.sendFailure(Component.literal("§c音乐文件过大，最大支持50MB"));
                return 0;
            }
            System.out.println("[Kyokuerabu] Music file: " + fileName + ", size: " + fileSize + " bytes");
        } catch (IOException e) {
            source.sendFailure(Component.literal("§c读取音乐文件信息失败: " + e.getMessage()));
            return 0;
        }

        // 使用新的缓存系统
        System.out.println("[Kyokuerabu] Using cache system for music playback");
        ServerMusicCacheManager.requestMusicPlay(targets, fileName, musicFilePath);

        source.sendSuccess(() -> Component.literal(
            "§a已向 " + targets.size() + " 个玩家发送音乐播放请求: " + fileName
        ), true);
        
        // 向命令执行者发送成功消息
        if (targets.size() == 1) {
            ServerPlayer target = targets.iterator().next();
            source.sendSuccess(() -> Component.literal(
                "§a已向玩家 " + target.getName().getString() + " 发送本地音乐播放指令: " + fileName
            ), true);
        } else {
            source.sendSuccess(() -> Component.literal(
                "§a已向 " + targets.size() + " 个玩家发送本地音乐播放指令: " + fileName
            ), true);
        }
        
        return targets.size();
    }

    private static void sendMusicAsSimplePacket(String fileName, byte[] musicData, Collection<ServerPlayer> targets) {
        System.out.println("[Kyokuerabu] Sending small music file " + fileName + " as simple packet");

        SimpleMusicPacket packet = new SimpleMusicPacket(fileName, musicData);

        for (ServerPlayer player : targets) {
            try {
                NetworkHandler.INSTANCE.send(
                    PacketDistributor.PLAYER.with(() -> player),
                    packet
                );
                System.out.println("[Kyokuerabu] Sent simple music packet to " + player.getName().getString());
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Failed to send simple packet to player " + player.getName().getString() + ": " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private static void sendMusicInChunks(String fileName, byte[] musicData, Collection<ServerPlayer> targets) {
        // 分块大小：8KB（更安全的网络包大小，避免连接丢失）
        final int CHUNK_SIZE = 8 * 1024;
        int totalChunks = (int) Math.ceil((double) musicData.length / CHUNK_SIZE);

        System.out.println("[Kyokuerabu] Sending music file " + fileName + " in " + totalChunks + " chunks");

        // 在后台线程中发送分块，避免阻塞服务器
        CompletableFuture.runAsync(() -> {
            try {
                for (int i = 0; i < totalChunks; i++) {
                    int start = i * CHUNK_SIZE;
                    int end = Math.min(start + CHUNK_SIZE, musicData.length);
                    int chunkLength = end - start;

                    byte[] chunkData = new byte[chunkLength];
                    System.arraycopy(musicData, start, chunkData, 0, chunkLength);

                    PlayLocalMusicChunkPacket packet = new PlayLocalMusicChunkPacket(
                        fileName, chunkData, i, totalChunks, musicData.length
                    );

                    // 发送给所有目标玩家
                    for (ServerPlayer player : targets) {
                        try {
                            NetworkHandler.INSTANCE.send(
                                PacketDistributor.PLAYER.with(() -> player),
                                packet
                            );
                        } catch (Exception e) {
                            System.out.println("[Kyokuerabu] Failed to send chunk " + (i + 1) + " to player " + player.getName().getString() + ": " + e.getMessage());
                        }
                    }

                    System.out.println("[Kyokuerabu] Sent chunk " + (i + 1) + "/" + totalChunks + " for " + fileName);

                    // 在分块之间添加延迟，避免网络拥塞
                    if (i < totalChunks - 1) {
                        Thread.sleep(100); // 增加延迟到100ms
                    }
                }

                System.out.println("[Kyokuerabu] Finished sending all chunks for " + fileName);

            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Error sending music chunks: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
    }
}
