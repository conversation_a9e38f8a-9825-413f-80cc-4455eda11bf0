package top.lacrus.kyokuerabu.client;

import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.Checkbox;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public class MusicConfigScreen extends Screen {
    private final Screen parentScreen;
    
    // UI组件
    private VolumeSlider volumeSlider;
    private Checkbox acceptOpMusicCheckbox;
    private Checkbox showHudCheckbox;
    private Checkbox showDownloadMessagesCheckbox;
    
    public MusicConfigScreen(Screen parentScreen) {
        super(Component.literal("音乐设置"));
        this.parentScreen = parentScreen;
    }
    
    @Override
    protected void init() {
        super.init();
        
        int centerX = this.width / 2;
        int startY = this.height / 4;
        int spacing = 25;
        
        // 音量滑块 - 使用安全的默认值
        double currentVolume = MusicConfig.getVolume();
        this.volumeSlider = new VolumeSlider(centerX - 100, startY, 200, 20,
            Component.literal("音量: "), currentVolume);
        this.addRenderableWidget(this.volumeSlider);
        
        // 接受OP音乐推送
        this.acceptOpMusicCheckbox = new Checkbox(centerX - 100, startY + spacing, 20, 20,
            Component.literal("接受OP的音乐推送"), MusicConfig.acceptOpMusic());
        this.addRenderableWidget(this.acceptOpMusicCheckbox);
        
        // 显示HUD
        this.showHudCheckbox = new Checkbox(centerX - 100, startY + spacing * 2, 20, 20,
            Component.literal("显示音乐HUD"), MusicConfig.showHud());
        this.addRenderableWidget(this.showHudCheckbox);
        
        // 显示下载消息
        this.showDownloadMessagesCheckbox = new Checkbox(centerX - 100, startY + spacing * 3, 20, 20,
            Component.literal("显示下载消息"), MusicConfig.showDownloadMessages());
        this.addRenderableWidget(this.showDownloadMessagesCheckbox);
        
        // 保存按钮
        this.addRenderableWidget(Button.builder(Component.literal("保存"), button -> {
            saveSettings();
            this.minecraft.setScreen(this.parentScreen);
        }).bounds(centerX - 50, startY + spacing * 5, 100, 20).build());
        
        // 取消按钮
        this.addRenderableWidget(Button.builder(Component.literal("取消"), button -> {
            this.minecraft.setScreen(this.parentScreen);
        }).bounds(centerX - 50, startY + spacing * 6, 100, 20).build());
    }
    
    private void saveSettings() {
        MusicConfig.setVolume(this.volumeSlider.getValue());
        MusicConfig.setAcceptOpMusic(this.acceptOpMusicCheckbox.selected());
        MusicConfig.setShowHud(this.showHudCheckbox.selected());
        MusicConfig.setShowDownloadMessages(this.showDownloadMessagesCheckbox.selected());

        // 安全保存配置
        try {
            MusicConfig.SPEC.save();
        } catch (Exception e) {
            // 配置保存失败时显示错误消息
            if (this.minecraft != null && this.minecraft.player != null) {
                this.minecraft.player.sendSystemMessage(
                    net.minecraft.network.chat.Component.literal("§c[Kyokuerabu] 配置保存失败")
                );
            }
        }

        // 如果HUD设置改变，更新HUD显示
        if (!MusicConfig.showHud()) {
            MusicHUD.hideHUD();
        } else if (MusicHUD.isPlaying()) {
            MusicHUD.showHUD();
        }
    }
    
    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        this.renderBackground(guiGraphics);
        
        // 绘制标题
        guiGraphics.drawCenteredString(this.font, this.title, this.width / 2, 20, 0xFFFFFF);
        
        // 绘制当前播放信息
        if (MusicHUD.isPlaying()) {
            String currentMusic = "当前播放: " + MusicHUD.getCurrentMusicName();
            guiGraphics.drawCenteredString(this.font, currentMusic, this.width / 2, 40, 0x55FF55);
        }
        
        super.render(guiGraphics, mouseX, mouseY, partialTick);
    }
    
    @Override
    public boolean isPauseScreen() {
        return false;
    }
}
