package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import org.slf4j.Logger;
import top.lacrus.kyokuerabu.network.MusicSegmentResponsePacket;

import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 客户端音乐分段接收器
 */
public class MusicSegmentReceiver {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static final Path CLIENT_MUSIC_DIR = Paths.get("client_music");
    
    // 下载状态跟踪
    private static final ConcurrentHashMap<String, DownloadState> downloadStates = new ConcurrentHashMap<>();
    
    private static class DownloadState {
        final String fileName;
        final int totalSegments;
        final AtomicInteger receivedSegments = new AtomicInteger(0);
        final boolean[] segmentReceived;
        final long fileSize;
        final String expectedHash;
        volatile boolean completed = false;
        volatile boolean failed = false;
        
        DownloadState(String fileName, int totalSegments, long fileSize, String expectedHash) {
            this.fileName = fileName;
            this.totalSegments = totalSegments;
            this.segmentReceived = new boolean[totalSegments];
            this.fileSize = fileSize;
            this.expectedHash = expectedHash;
        }
        
        boolean isComplete() {
            return receivedSegments.get() == totalSegments;
        }
        
        double getProgress() {
            return (double) receivedSegments.get() / totalSegments;
        }
    }

    public static void startDownload(String fileName, long fileSize, String expectedHash, int totalSegments) {
        try {
            // 确保目录存在
            if (!Files.exists(CLIENT_MUSIC_DIR)) {
                Files.createDirectories(CLIENT_MUSIC_DIR);
            }
            
            // 创建下载状态
            DownloadState state = new DownloadState(fileName, totalSegments, fileSize, expectedHash);
            downloadStates.put(fileName, state);
            
            // 创建空文件
            Path musicFile = CLIENT_MUSIC_DIR.resolve(fileName);
            Files.deleteIfExists(musicFile);
            try (RandomAccessFile file = new RandomAccessFile(musicFile.toFile(), "rw")) {
                file.setLength(fileSize);
            }
            
            LOGGER.info("Started segmented download: {} ({} segments, {} bytes)", fileName, totalSegments, fileSize);
            
        } catch (Exception e) {
            LOGGER.error("Failed to start download: {}", e.getMessage());
            downloadStates.remove(fileName);
        }
    }

    public static void handleSegmentResponse(MusicSegmentResponsePacket packet) {
        String fileName = packet.getFileName();
        DownloadState state = downloadStates.get(fileName);
        
        if (state == null) {
            LOGGER.warn("Received segment for unknown download: {}", fileName);
            return;
        }
        
        if (state.completed || state.failed) {
            return;
        }

        try {
            // 写入分段数据
            writeSegmentData(packet, state);
            
            // 更新状态
            int segmentIndex = packet.getSegmentIndex();
            if (!state.segmentReceived[segmentIndex]) {
                state.segmentReceived[segmentIndex] = true;
                int received = state.receivedSegments.incrementAndGet();
                
                // 更新进度（移除HUD显示）
                double progress = state.getProgress();
                
                LOGGER.debug("Received segment {}/{} for {} ({:.1f}%)",
                           received, state.totalSegments, fileName, progress * 100);
                
                // 检查是否完成
                if (state.isComplete()) {
                    completeDownload(state);
                }
            }
            
        } catch (Exception e) {
            LOGGER.error("Failed to handle segment: {}", e.getMessage());
            failDownload(state, e.getMessage());
        }
    }

    private static void writeSegmentData(MusicSegmentResponsePacket packet, DownloadState state) throws IOException {
        Path musicFile = CLIENT_MUSIC_DIR.resolve(state.fileName);
        
        try (RandomAccessFile file = new RandomAccessFile(musicFile.toFile(), "rw")) {
            file.seek(packet.getStartByte());
            file.write(packet.getSegmentData());
        }
    }

    private static void completeDownload(DownloadState state) {
        try {
            state.completed = true;
            downloadStates.remove(state.fileName);
            
            LOGGER.info("Download completed: {}", state.fileName);
            
            // 验证文件并播放
            Path musicFile = CLIENT_MUSIC_DIR.resolve(state.fileName);
            verifyAndPlay(musicFile, state.fileName, state.expectedHash);
            
        } catch (Exception e) {
            LOGGER.error("Failed to complete download: {}", e.getMessage());
            failDownload(state, e.getMessage());
        }
    }

    private static void failDownload(DownloadState state, String error) {
        state.failed = true;
        downloadStates.remove(state.fileName);
        
        LOGGER.error("Download failed: {} - {}", state.fileName, error);
        
        // 清理文件
        try {
            Path musicFile = CLIENT_MUSIC_DIR.resolve(state.fileName);
            Files.deleteIfExists(musicFile);
        } catch (Exception e) {
            LOGGER.warn("Failed to cleanup failed download: {}", e.getMessage());
        }
        
        // 发送错误消息
        sendMessage("§c[Kyokuerabu] 下载失败: " + error);
    }

    private static void verifyAndPlay(Path musicFile, String fileName, String expectedHash) {
        try {
            // 验证文件哈希
            String actualHash = MusicCacheManager.calculateFileHash(musicFile);
            if (!actualHash.equals(expectedHash)) {
                LOGGER.error("Hash verification failed for {}", fileName);
                sendMessage("§c[Kyokuerabu] 文件哈希验证失败");
                Files.deleteIfExists(musicFile);
                return;
            }
            
            LOGGER.info("File hash verified, starting playback: {}", fileName);
            sendMessage("§a[Kyokuerabu] 文件验证成功");
            
            // 获取音乐时长并播放
            System.out.println("[Kyokuerabu] MusicSegmentReceiver: Getting duration for " + fileName);
            System.out.println("[Kyokuerabu] Music file path: " + musicFile);

            MusicDurationReader.getDurationAsync(musicFile, duration -> {
                System.out.println("[Kyokuerabu] MusicSegmentReceiver: Duration callback received: " + duration + "ms");
                String displayName = fileName.replaceAll("\\.[^.]*$", "");
                if (MusicConfig.showHud()) {
                    System.out.println("[Kyokuerabu] MusicSegmentReceiver: Updating HUD with duration");
                    MusicHUD.setCurrentMusic(displayName, getCurrentPlayerName(), duration);
                } else {
                    System.out.println("[Kyokuerabu] MusicSegmentReceiver: HUD disabled, skipping HUD update");
                }

                // 使用MinecraftJLayerPlayer播放
                System.out.println("[Kyokuerabu] MusicSegmentReceiver: Starting playback");
                MinecraftJLayerPlayer.playMusicFile(musicFile, fileName);
            });
            
        } catch (Exception e) {
            LOGGER.error("Failed to verify and play: {}", e.getMessage());
            sendMessage("§c[Kyokuerabu] 验证播放失败: " + e.getMessage());
        }
    }

    private static String getCurrentPlayerName() {
        try {
            net.minecraft.client.Minecraft mc = net.minecraft.client.Minecraft.getInstance();
            if (mc.player != null) {
                return mc.player.getName().getString();
            }
        } catch (Exception e) {
            LOGGER.warn("Failed to get player name: {}", e.getMessage());
        }
        return "Unknown";
    }

    private static void sendMessage(String message) {
        try {
            net.minecraft.client.Minecraft.getInstance().execute(() -> {
                if (net.minecraft.client.Minecraft.getInstance().player != null) {
                    net.minecraft.client.Minecraft.getInstance().player.sendSystemMessage(
                        net.minecraft.network.chat.Component.literal(message)
                    );
                }
            });
        } catch (Exception e) {
            LOGGER.error("Failed to send message: {}", e.getMessage());
        }
    }

    public static boolean isDownloading(String fileName) {
        return downloadStates.containsKey(fileName);
    }

    public static double getDownloadProgress(String fileName) {
        DownloadState state = downloadStates.get(fileName);
        return state != null ? state.getProgress() : 0.0;
    }
}
