package top.lacrus.kyokuerabu.client;

import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.Font;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.RenderGuiOverlayEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@OnlyIn(Dist.CLIENT)
@Mod.EventBusSubscriber(modid = "kyokuerabu", value = Dist.CLIENT)
public class MusicHUD {
    private static String currentMusicName = null;
    private static String currentPlayerName = null;
    private static long musicStartTime = 0;
    private static boolean isVisible = false;
    private static long estimatedDuration = 0; // 估计的音乐总时长（毫秒）

    public static void setCurrentMusic(String musicName, String playerName) {
        currentMusicName = musicName;
        currentPlayerName = playerName;
        musicStartTime = System.currentTimeMillis();
        isVisible = true;

        // 估计音乐时长（基于文件大小的粗略估计）
        estimatedDuration = estimateMusicDuration(musicName);
    }

    public static void setCurrentMusic(String musicName, String playerName, long duration) {
        System.out.println("[Kyokuerabu] MusicHUD.setCurrentMusic called:");
        System.out.println("  Music: " + musicName);
        System.out.println("  Player: " + playerName);
        System.out.println("  Duration: " + duration + "ms (" + (duration/1000) + "s)");

        currentMusicName = musicName;
        currentPlayerName = playerName;
        musicStartTime = System.currentTimeMillis();
        isVisible = true;
        estimatedDuration = duration;

        System.out.println("[Kyokuerabu] MusicHUD updated successfully");
    }

    public static void clearCurrentMusic() {
        currentMusicName = null;
        currentPlayerName = null;
        musicStartTime = 0;
        isVisible = false;
        estimatedDuration = 0;
    }

    @SubscribeEvent
    public static void onRenderGuiOverlay(RenderGuiOverlayEvent.Post event) {
        if (!isVisible || currentMusicName == null || !MusicConfig.showHud()) {
            return;
        }

        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.options.hideGui) {
            return;
        }

        GuiGraphics guiGraphics = event.getGuiGraphics();
        Font font = mc.font;
        
        int screenWidth = mc.getWindow().getGuiScaledWidth();
        int screenHeight = mc.getWindow().getGuiScaledHeight();

        // HUD位置：右上角
        int hudX = screenWidth - 10;
        int hudY = 10;

        // 计算播放时间
        long playTime = (System.currentTimeMillis() - musicStartTime) / 1000;
        String timeStr = formatTime(playTime);

        // 计算进度
        double progress = 0.0;
        String progressText = timeStr;
        if (estimatedDuration > 0) {
            long currentTime = System.currentTimeMillis() - musicStartTime;
            progress = Math.min(1.0, (double) currentTime / estimatedDuration);
            String totalTimeStr = formatTime(estimatedDuration / 1000);
            progressText = timeStr + " / " + totalTimeStr;
        }

        // 准备显示文本
        String musicText = "♪ " + currentMusicName;
        String playerText = "播放者: " + currentPlayerName;
        String timeText = progressText;

        // 计算文本宽度
        int musicWidth = font.width(musicText);
        int playerWidth = font.width(playerText);
        int timeWidth = font.width(timeText);
        int maxWidth = Math.max(Math.max(musicWidth, playerWidth), timeWidth);

        // 确保进度条宽度不小于文本宽度
        int progressBarWidth = Math.max(maxWidth, 150);

        // HUD背景 (增加高度以容纳进度条)
        int bgX = hudX - progressBarWidth - 15;
        int bgY = hudY - 5;
        int bgWidth = progressBarWidth + 20;
        int bgHeight = 65; // 增加高度

        // 绘制半透明背景
        guiGraphics.fill(bgX, bgY, bgX + bgWidth, bgY + bgHeight, 0x80000000);

        // 绘制边框
        guiGraphics.fill(bgX, bgY, bgX + bgWidth, bgY + 1, 0xFF555555); // 上边框
        guiGraphics.fill(bgX, bgY + bgHeight - 1, bgX + bgWidth, bgY + bgHeight, 0xFF555555); // 下边框
        guiGraphics.fill(bgX, bgY, bgX + 1, bgY + bgHeight, 0xFF555555); // 左边框
        guiGraphics.fill(bgX + bgWidth - 1, bgY, bgX + bgWidth, bgY + bgHeight, 0xFF555555); // 右边框

        // 绘制文本
        int textX = hudX - progressBarWidth - 5;
        int textY = hudY;

        // 音乐名称 (绿色)
        guiGraphics.drawString(font, musicText, textX, textY, 0x55FF55);
        textY += 12;

        // 播放者 (黄色)
        guiGraphics.drawString(font, playerText, textX, textY, 0xFFFF55);
        textY += 12;

        // 播放时间 (白色)
        guiGraphics.drawString(font, timeText, textX, textY, 0xFFFFFF);
        textY += 12;

        // 绘制进度条
        int progressBarX = textX;
        int progressBarY = textY + 2;
        int progressBarHeight = 4;

        // 进度条背景 (深灰色)
        guiGraphics.fill(progressBarX, progressBarY, progressBarX + progressBarWidth, progressBarY + progressBarHeight, 0xFF333333);

        // 进度条填充 (绿色)
        if (progress > 0) {
            int fillWidth = (int) (progressBarWidth * progress);
            guiGraphics.fill(progressBarX, progressBarY, progressBarX + fillWidth, progressBarY + progressBarHeight, 0xFF55FF55);
        }

        // 进度条边框 (浅灰色)
        guiGraphics.fill(progressBarX - 1, progressBarY - 1, progressBarX + progressBarWidth + 1, progressBarY, 0xFF666666); // 上边框
        guiGraphics.fill(progressBarX - 1, progressBarY + progressBarHeight, progressBarX + progressBarWidth + 1, progressBarY + progressBarHeight + 1, 0xFF666666); // 下边框
        guiGraphics.fill(progressBarX - 1, progressBarY - 1, progressBarX, progressBarY + progressBarHeight + 1, 0xFF666666); // 左边框
        guiGraphics.fill(progressBarX + progressBarWidth, progressBarY - 1, progressBarX + progressBarWidth + 1, progressBarY + progressBarHeight + 1, 0xFF666666); // 右边框
    }

    private static String formatTime(long seconds) {
        long minutes = seconds / 60;
        long remainingSeconds = seconds % 60;
        return String.format("%02d:%02d", minutes, remainingSeconds);
    }

    private static long estimateMusicDuration(String musicName) {
        try {
            // 尝试从client_music目录获取文件信息
            java.nio.file.Path musicFile = java.nio.file.Paths.get("client_music", musicName + ".mp3");
            if (!java.nio.file.Files.exists(musicFile)) {
                musicFile = java.nio.file.Paths.get("client_music", musicName + ".ogg");
            }
            if (!java.nio.file.Files.exists(musicFile)) {
                musicFile = java.nio.file.Paths.get("client_music", musicName);
            }

            if (java.nio.file.Files.exists(musicFile)) {
                // 使用MusicDurationReader获取准确时长
                long duration = MusicDurationReader.getDurationMillis(musicFile);
                if (duration > 0) {
                    return duration;
                }

                // 如果获取失败，使用文件大小估算
                long fileSize = java.nio.file.Files.size(musicFile);
                long estimatedSeconds = fileSize / (1024 * 1024 / 60);
                return estimatedSeconds * 1000;
            }
        } catch (Exception e) {
            // 获取文件信息失败
        }

        // 默认估计3分钟
        return 3 * 60 * 1000;
    }

    // 检查播放器进程是否还在运行
    public static void checkPlayerStatus() {
        if (isVisible && currentMusicName != null) {
            // 检查外部播放器进程是否还在运行
            if (!MinecraftJLayerPlayer.isPlayerRunning()) {
                // 进程结束，自动清除HUD
                clearCurrentMusic();
            }
        }
    }

    // 手动隐藏HUD
    public static void hideHUD() {
        isVisible = false;
    }

    // 手动显示HUD
    public static void showHUD() {
        if (currentMusicName != null) {
            isVisible = true;
        }
    }

    // 获取当前播放状态
    public static boolean isPlaying() {
        return isVisible && currentMusicName != null;
    }

    // 获取当前音乐名称
    public static String getCurrentMusicName() {
        return currentMusicName;
    }

    // 获取当前播放者
    public static String getCurrentPlayerName() {
        return currentPlayerName;
    }
}
