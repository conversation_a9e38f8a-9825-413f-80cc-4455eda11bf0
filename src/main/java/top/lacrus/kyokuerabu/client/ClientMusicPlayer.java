package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import javazoom.jl.decoder.JavaLayerException;
import javazoom.jl.player.Player;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import org.slf4j.Logger;

import javax.sound.sampled.*;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.concurrent.CompletableFuture;

@OnlyIn(Dist.CLIENT)
public class ClientMusicPlayer {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static Player currentMp3Player;
    private static Thread currentMp3Thread;
    private static Clip currentClip;
    private static volatile boolean shouldStop = false;
    private static Path tempMusicFile = null;
    
    public static void playMusic(String musicUrl) {
        System.out.println("[Kyokuerabu] ClientMusicPlayer.playMusic called with URL: " + musicUrl);
        LOGGER.info("ClientMusicPlayer.playMusic called with URL: {}", musicUrl);

        // 停止当前播放的音乐
        stopCurrentMusic();

        // 在后台线程中下载并播放音乐
        CompletableFuture.runAsync(() -> {
            try {
                System.out.println("[Kyokuerabu] Starting download and playback process");
                
                // 下载音乐到临时文件
                Path downloadedFile = downloadMusicToTemp(musicUrl);
                if (downloadedFile != null) {
                    tempMusicFile = downloadedFile;
                    
                    // 根据文件类型播放音乐
                    String urlLower = musicUrl.toLowerCase();
                    if (urlLower.endsWith(".mp3")) {
                        playMp3FromFile(downloadedFile, musicUrl);
                    } else {
                        playStandardFromFile(downloadedFile, musicUrl);
                    }
                }
                
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Exception in playMusic: " + e.getClass().getSimpleName() + ": " + e.getMessage());
                e.printStackTrace();
                LOGGER.error("Failed to play music: {}", musicUrl, e);
            }
        });
    }
    
    private static Path downloadMusicToTemp(String musicUrl) {
        try {
            System.out.println("[Kyokuerabu] Starting download from URL: " + musicUrl);
            
            URL url = new URL(musicUrl);
            String fileName = Paths.get(url.getPath()).getFileName().toString();
            if (fileName.isEmpty()) {
                fileName = "music_" + System.currentTimeMillis();
            }
            
            // 创建临时文件
            Path tempFile = Files.createTempFile("kyokuerabu_music_", "_" + fileName);
            
            // 下载文件
            try (InputStream in = new BufferedInputStream(url.openStream())) {
                Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);
                System.out.println("[Kyokuerabu] Downloaded music to: " + tempFile);
                return tempFile;
            }
            
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Failed to download music: " + e.getMessage());
            LOGGER.error("Failed to download music from: {}", musicUrl, e);
            
            // 向玩家发送错误消息
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(
                        Component.literal("§c[Kyokuerabu] 下载音乐失败: " + e.getMessage())
                    );
                }
            });
            return null;
        }
    }
    
    private static void playMp3FromFile(Path filePath, String musicUrl) {
        try {
            System.out.println("[Kyokuerabu] Starting MP3 playback from file: " + filePath);
            
            FileInputStream fis = new FileInputStream(filePath.toFile());
            BufferedInputStream bis = new BufferedInputStream(fis);
            currentMp3Player = new Player(bis);
            shouldStop = false;
            
            System.out.println("[Kyokuerabu] MP3 Player initialized successfully");
            
            // 在新线程中播放MP3
            currentMp3Thread = new Thread(() -> {
                try {
                    // 向玩家发送开始播放消息
                    Minecraft.getInstance().execute(() -> {
                        if (Minecraft.getInstance().player != null) {
                            Minecraft.getInstance().player.sendSystemMessage(
                                Component.literal("§a[Kyokuerabu] 开始播放MP3音乐: " + musicUrl)
                            );
                        }
                    });

                    LOGGER.info("Started playing MP3 music: {}", musicUrl);

                    // 播放音乐
                    currentMp3Player.play();

                    // 播放完成
                    if (!shouldStop) {
                        Minecraft.getInstance().execute(() -> {
                            if (Minecraft.getInstance().player != null) {
                                Minecraft.getInstance().player.sendSystemMessage(
                                    Component.literal("§e[Kyokuerabu] 音乐播放完成")
                                );
                            }
                        });
                    }

                } catch (JavaLayerException e) {
                    if (!shouldStop) {
                        LOGGER.error("Error during MP3 playback: {}", musicUrl, e);
                    }
                } finally {
                    cleanup();
                }
            });
            
            currentMp3Thread.setDaemon(true);
            currentMp3Thread.start();
            
            System.out.println("[Kyokuerabu] MP3 playback thread started");
            
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error initializing MP3 player: " + e.getMessage());
            LOGGER.error("Error initializing MP3 player for: {}", musicUrl, e);
        }
    }
    
    private static void playStandardFromFile(Path filePath, String musicUrl) {
        try {
            System.out.println("[Kyokuerabu] Starting standard audio playback from file: " + filePath);
            
            FileInputStream fis = new FileInputStream(filePath.toFile());
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(
                new BufferedInputStream(fis)
            );
            
            currentClip = AudioSystem.getClip();
            currentClip.open(audioInputStream);
            
            // 设置音量
            try {
                FloatControl volumeControl = (FloatControl) currentClip.getControl(FloatControl.Type.MASTER_GAIN);
                volumeControl.setValue(-10.0f); // 降低音量
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Could not set volume control: " + e.getMessage());
            }
            
            // 向玩家发送开始播放消息
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(
                        Component.literal("§a[Kyokuerabu] 开始播放音乐: " + musicUrl)
                    );
                }
            });
            
            currentClip.start();
            
            System.out.println("[Kyokuerabu] Standard audio playback started");
            LOGGER.info("Started playing music: {}", musicUrl);
            
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error playing standard audio: " + e.getMessage());
            LOGGER.error("Error playing standard audio for: {}", musicUrl, e);
        }
    }
    
    public static void stopCurrentMusic() {
        shouldStop = true;
        
        if (currentMp3Player != null) {
            try {
                currentMp3Player.close();
                System.out.println("[Kyokuerabu] Stopped current MP3 player");
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Error stopping MP3 player: " + e.getMessage());
            }
        }
        
        if (currentClip != null) {
            try {
                currentClip.stop();
                currentClip.close();
                System.out.println("[Kyokuerabu] Stopped current audio clip");
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Error stopping audio clip: " + e.getMessage());
            }
        }
        
        if (currentMp3Thread != null && currentMp3Thread.isAlive()) {
            try {
                currentMp3Thread.interrupt();
                currentMp3Thread.join(1000); // 等待最多1秒
                System.out.println("[Kyokuerabu] Stopped current MP3 thread");
            } catch (InterruptedException e) {
                System.out.println("[Kyokuerabu] Interrupted while stopping MP3 thread");
                Thread.currentThread().interrupt();
            }
        }
        
        cleanup();
    }
    
    private static void cleanup() {
        currentMp3Player = null;
        currentClip = null;
        currentMp3Thread = null;
        shouldStop = false;
        
        // 清理临时文件
        if (tempMusicFile != null) {
            try {
                Files.deleteIfExists(tempMusicFile);
                System.out.println("[Kyokuerabu] Cleanup: Deleted temporary music file");
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Cleanup: Failed to delete temporary file: " + e.getMessage());
            }
            tempMusicFile = null;
        }
        
        System.out.println("[Kyokuerabu] Music player cleanup completed");
    }
    
    // 添加一个静态块来在JVM关闭时清理临时文件
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (tempMusicFile != null) {
                try {
                    Files.deleteIfExists(tempMusicFile);
                    System.out.println("[Kyokuerabu] Cleanup: Deleted temporary music file on shutdown");
                } catch (Exception e) {
                    System.out.println("[Kyokuerabu] Cleanup: Failed to delete temporary file on shutdown: " + e.getMessage());
                }
            }
        }));
    }
}
