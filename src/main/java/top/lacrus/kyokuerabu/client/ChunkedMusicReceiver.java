package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@OnlyIn(Dist.CLIENT)
public class ChunkedMusicReceiver {
    private static final Logger LOGGER = LogUtils.getLogger();
    
    // 存储正在接收的音乐文件数据
    private static final Map<String, MusicFileBuffer> receivingFiles = new ConcurrentHashMap<>();
    
    public static void receiveChunk(String fileName, byte[] chunkData, int chunkIndex, int totalChunks, int totalSize) {
        LOGGER.info("Receiving chunk " + chunkIndex + "/" + totalChunks + " for " + fileName);
        
        // 获取或创建文件缓冲区
        MusicFileBuffer buffer = receivingFiles.computeIfAbsent(fileName, 
            k -> new MusicFileBuffer(fileName, totalChunks, totalSize));
        
        // 添加分块数据
        buffer.addChunk(chunkIndex, chunkData);
        
        // 更新进度
        int receivedChunks = buffer.getReceivedChunks();
        sendProgressMessage(fileName, receivedChunks, totalChunks);
        
        // 检查是否接收完成
        if (buffer.isComplete()) {
            LOGGER.info("All chunks received for " + fileName);
            sendMessageToPlayer("§a[Kyokuerabu] 文件接收完成，开始播放: " + fileName);

            try {
                byte[] completeData = buffer.getCompleteData();
                LOGGER.info("Assembled complete music data: " + completeData.length + " bytes");

                // 清理缓冲区
                receivingFiles.remove(fileName);

                // 先保存文件，然后使用JLayer播放
                LOGGER.info("Starting music save and JLayer playback for: " + fileName);
                saveAndPlayMusicWithJLayer(fileName, completeData);

            } catch (Exception e) {
                LOGGER.error("Error assembling music data: " + e.getMessage(), e);
                LOGGER.error("Error assembling music data for: {}", fileName, e);

                sendMessageToPlayer("§c[Kyokuerabu] 组装音乐数据失败: " + e.getMessage());
                receivingFiles.remove(fileName);
            }
        }
    }
    
    private static void sendProgressMessage(String fileName, int received, int total) {
        int percentage = (received * 100) / total;

        // 显示进度：10%, 20%, 30%, ..., 100%
        if (received == 1) {
            sendMessageToPlayer("§e[Kyokuerabu] 开始接收音乐文件: " + fileName);
        } else if (percentage % 10 == 0 && percentage > 0) {
            // 确保每个10%的倍数只显示一次
            int lastPercentage = ((received - 1) * 100) / total;
            if (lastPercentage / 10 != percentage / 10) {
                sendMessageToPlayer("§e[Kyokuerabu] 接收进度: " + percentage + "%");
            }
        }
    }
    
    private static void sendMessageToPlayer(String message) {
        try {
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(Component.literal(message));
                }
            });
        } catch (Exception e) {
            LOGGER.error("Failed to send message: " + e.getMessage(), e);
        }
    }
    
    private static void saveAndPlayMusicWithJLayer(String fileName, byte[] musicData) {
        LOGGER.info("saveAndPlayMusic called for: " + fileName);
        LOGGER.info("Data size: " + musicData.length + " bytes");

        try {
            // 创建client_music目录
            java.nio.file.Path clientMusicDir = java.nio.file.Paths.get("client_music");
            if (!java.nio.file.Files.exists(clientMusicDir)) {
                java.nio.file.Files.createDirectories(clientMusicDir);
                LOGGER.info("Created client_music directory: " + clientMusicDir.toAbsolutePath());
                sendMessageToPlayer("§a[Kyokuerabu] 已创建客户端音乐目录");
            }

            // 保存文件
            java.nio.file.Path musicFile = clientMusicDir.resolve(fileName);
            java.nio.file.Files.write(musicFile, musicData);

            LOGGER.info("✓ File saved successfully: " + musicFile.toAbsolutePath());
            LOGGER.info("✓ File size: " + java.nio.file.Files.size(musicFile) + " bytes");
            if (MusicConfig.showDownloadMessages()) {
                sendMessageToPlayer("§a[Kyokuerabu] ✓ 文件已保存: " + fileName);
                sendMessageToPlayer("§a[Kyokuerabu] ✓ 保存位置: " + musicFile.toAbsolutePath());
            }

            // 验证文件确实存在
            if (java.nio.file.Files.exists(musicFile)) {
                // 不再显示"验证成功开始播放"，由MinecraftJLayerPlayer统一显示"开始播放:xxxx"

                // 现在使用MinecraftJLayerPlayer播放
                LOGGER.info("Starting playback with MinecraftJLayerPlayer...");
                MinecraftJLayerPlayer.playMusicFromMemory(fileName, musicData);

            } else {
                sendMessageToPlayer("§c[Kyokuerabu] ✗ 文件验证失败");
                LOGGER.error("✗ File verification failed");
            }

        } catch (Exception e) {
            LOGGER.error("✗ Error saving file: " + e.getMessage(), e);
            sendMessageToPlayer("§c[Kyokuerabu] ✗ 保存文件失败: " + e.getMessage());

            // 如果保存失败，尝试直接用MinecraftJLayerPlayer播放
            LOGGER.info("Trying direct MinecraftJLayerPlayer playback as fallback...");
            sendMessageToPlayer("§e[Kyokuerabu] 尝试直接用MinecraftJLayerPlayer播放...");
            MinecraftJLayerPlayer.playMusicFromMemory(fileName, musicData);
        }
    }

    // 清理超时的接收任务
    public static void cleanup() {
        receivingFiles.clear();
    }
    
    // 内部类：音乐文件缓冲区
    private static class MusicFileBuffer {
        private final String fileName;
        private final int totalChunks;
        private final int totalSize;
        private final Map<Integer, byte[]> chunks;
        private final boolean[] receivedFlags;
        
        public MusicFileBuffer(String fileName, int totalChunks, int totalSize) {
            this.fileName = fileName;
            this.totalChunks = totalChunks;
            this.totalSize = totalSize;
            this.chunks = new HashMap<>();
            this.receivedFlags = new boolean[totalChunks];
        }
        
        public synchronized void addChunk(int chunkIndex, byte[] chunkData) {
            if (chunkIndex >= 0 && chunkIndex < totalChunks && !receivedFlags[chunkIndex]) {
                chunks.put(chunkIndex, chunkData);
                receivedFlags[chunkIndex] = true;
                LOGGER.info("Added chunk " + chunkIndex + " for " + fileName);
            }
        }
        
        public synchronized int getReceivedChunks() {
            int count = 0;
            for (boolean received : receivedFlags) {
                if (received) count++;
            }
            return count;
        }
        
        public synchronized boolean isComplete() {
            for (boolean received : receivedFlags) {
                if (!received) return false;
            }
            return true;
        }
        
        public synchronized byte[] getCompleteData() throws Exception {
            if (!isComplete()) {
                throw new IllegalStateException("Not all chunks received");
            }
            
            byte[] result = new byte[totalSize];
            int offset = 0;
            
            for (int i = 0; i < totalChunks; i++) {
                byte[] chunkData = chunks.get(i);
                if (chunkData == null) {
                    throw new IllegalStateException("Missing chunk: " + i);
                }
                
                System.arraycopy(chunkData, 0, result, offset, chunkData.length);
                offset += chunkData.length;
            }
            
            if (offset != totalSize) {
                throw new IllegalStateException("Size mismatch: expected " + totalSize + ", got " + offset);
            }
            
            return result;
        }
    }
}
