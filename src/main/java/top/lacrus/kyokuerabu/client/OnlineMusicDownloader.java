package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import org.slf4j.Logger;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.concurrent.CompletableFuture;

/**
 * 在线音乐下载器
 * 从URL下载音乐文件到client_music目录并播放
 */
public class OnlineMusicDownloader {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static final Path CLIENT_MUSIC_DIR = Paths.get("client_music");
    private static final int CONNECT_TIMEOUT = 10000; // 10秒
    private static final int READ_TIMEOUT = 30000; // 30秒

    public static void downloadAndPlay(String musicUrl, String fileName) {
        CompletableFuture.runAsync(() -> {
            try {
                LOGGER.info("Starting online music download: {} from {}", fileName, musicUrl);

                // 确保client_music目录存在
                if (!Files.exists(CLIENT_MUSIC_DIR)) {
                    Files.createDirectories(CLIENT_MUSIC_DIR);
                }

                // 检查文件是否已存在
                Path musicFile = CLIENT_MUSIC_DIR.resolve(fileName);
                if (Files.exists(musicFile)) {
                    LOGGER.info("Music file already exists, playing directly: {}", fileName);
                    playMusicFile(musicFile, fileName);
                    return;
                }

                // 下载文件
                if (downloadFile(musicUrl, musicFile)) {
                    LOGGER.info("Download completed successfully: {}", fileName);
                    // 获取音乐时长并播放
                    playMusicFile(musicFile, fileName);
                } else {
                    LOGGER.error("Download failed: {}", fileName);
                    sendMessage("§c[Kyokuerabu] 下载失败");
                }

            } catch (Exception e) {
                LOGGER.error("Error downloading online music: {}", e.getMessage());
                sendMessage("§c[Kyokuerabu] 下载出错: " + e.getMessage());
            }
        });
    }

    private static boolean downloadFile(String urlString, Path outputPath) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            
            // 设置User-Agent避免某些服务器拒绝请求
            connection.setRequestProperty("User-Agent", "Kyokuerabu-Music-Player/1.0");

            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                LOGGER.error("HTTP error code: {} for URL: {}", responseCode, urlString);
                return false;
            }

            long contentLength = connection.getContentLengthLong();
            LOGGER.info("Downloading file: {} bytes", contentLength > 0 ? contentLength : "unknown size");

            // 下载文件
            try (InputStream in = connection.getInputStream();
                 OutputStream out = Files.newOutputStream(outputPath)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytesRead = 0;
                long lastReportTime = System.currentTimeMillis();

                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;

                    // 每5秒报告一次进度
                    long currentTime = System.currentTimeMillis();
                    if (currentTime - lastReportTime > 5000) {
                        if (contentLength > 0) {
                            double progress = (double) totalBytesRead / contentLength * 100;
                            LOGGER.info("Download progress: {:.1f}% ({} / {} bytes)", 
                                       progress, totalBytesRead, contentLength);
                            if (MusicConfig.showDownloadMessages()) {
                                sendMessage(String.format("§e[Kyokuerabu] 下载进度: %.1f%%", progress));
                            }
                        } else {
                            LOGGER.info("Downloaded: {} bytes", totalBytesRead);
                        }
                        lastReportTime = currentTime;
                    }
                }

                LOGGER.info("Download completed: {} bytes", totalBytesRead);
            }

            connection.disconnect();
            return true;

        } catch (Exception e) {
            LOGGER.error("Download failed: {}", e.getMessage());
            try {
                Files.deleteIfExists(outputPath); // 清理失败的下载
            } catch (Exception cleanupEx) {
                LOGGER.warn("Failed to cleanup failed download: {}", cleanupEx.getMessage());
            }
            return false;
        }
    }

    private static void playMusicFile(Path musicFile, String fileName) {
        try {
            // 获取音乐时长
            System.out.println("[Kyokuerabu] OnlineMusicDownloader: Getting duration for " + fileName);
            System.out.println("[Kyokuerabu] Music file path: " + musicFile);

            // 更新HUD显示
            String displayName = fileName.replaceAll("\\.[^.]*$", ""); // 移除扩展名
            if (MusicConfig.showHud()) {
                System.out.println("[Kyokuerabu] OnlineMusicDownloader: Updating HUD");
                MusicHUD.setCurrentMusic(displayName, getCurrentPlayerName());
            } else {
                System.out.println("[Kyokuerabu] OnlineMusicDownloader: HUD disabled, skipping HUD update");
            }

            // 播放音乐
            System.out.println("[Kyokuerabu] OnlineMusicDownloader: Starting playback");
            MinecraftJLayerPlayer.playMusicFile(musicFile, fileName);
            sendMessage("§a[Kyokuerabu] ♪ 开始播放: " + displayName);

        } catch (Exception e) {
            LOGGER.error("Failed to play music file: {}", e.getMessage());
            sendMessage("§c[Kyokuerabu] 播放失败: " + e.getMessage());
        }
    }

    private static String getCurrentPlayerName() {
        try {
            net.minecraft.client.Minecraft mc = net.minecraft.client.Minecraft.getInstance();
            if (mc.player != null) {
                return mc.player.getName().getString();
            }
        } catch (Exception e) {
            LOGGER.warn("Failed to get player name: {}", e.getMessage());
        }
        return "Unknown";
    }

    private static void sendMessage(String message) {
        try {
            net.minecraft.client.Minecraft.getInstance().execute(() -> {
                if (net.minecraft.client.Minecraft.getInstance().player != null) {
                    net.minecraft.client.Minecraft.getInstance().player.sendSystemMessage(
                        net.minecraft.network.chat.Component.literal(message)
                    );
                }
            });
        } catch (Exception e) {
            LOGGER.error("Failed to send message: {}", e.getMessage());
        }
    }

    /**
     * 检查URL是否为支持的音频格式
     */
    public static boolean isSupportedAudioFormat(String url) {
        String lowerUrl = url.toLowerCase();
        return lowerUrl.endsWith(".mp3") || 
               lowerUrl.endsWith(".ogg") || 
               lowerUrl.endsWith(".wav") || 
               lowerUrl.endsWith(".flac") ||
               lowerUrl.contains(".mp3?") ||
               lowerUrl.contains(".ogg?") ||
               lowerUrl.contains(".wav?") ||
               lowerUrl.contains(".flac?");
    }

    /**
     * 从URL提取文件名
     */
    public static String extractFileName(String url) {
        try {
            // 移除查询参数
            String cleanUrl = url.split("\\?")[0];
            
            // 提取文件名
            String fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1);
            
            // 如果没有扩展名，尝试从URL推断
            if (!fileName.contains(".")) {
                if (url.toLowerCase().contains("mp3")) {
                    fileName += ".mp3";
                } else if (url.toLowerCase().contains("ogg")) {
                    fileName += ".ogg";
                } else if (url.toLowerCase().contains("wav")) {
                    fileName += ".wav";
                } else if (url.toLowerCase().contains("flac")) {
                    fileName += ".flac";
                } else {
                    fileName += ".mp3"; // 默认扩展名
                }
            }
            
            // 清理文件名中的非法字符
            fileName = fileName.replaceAll("[^a-zA-Z0-9._-]", "_");
            
            return fileName;
            
        } catch (Exception e) {
            LOGGER.warn("Failed to extract filename from URL: {}", url);
            return "online_music_" + System.currentTimeMillis() + ".mp3";
        }
    }
}
