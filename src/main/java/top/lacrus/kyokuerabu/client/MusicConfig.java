package top.lacrus.kyokuerabu.client;

import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.fml.ModLoadingContext;
import net.minecraftforge.fml.config.ModConfig;

public class MusicConfig {
    public static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();
    public static final ForgeConfigSpec SPEC;

    // 音量控制 (0.0 - 1.0)
    public static final ForgeConfigSpec.DoubleValue VOLUME;
    
    // 是否接受OP的音乐推送
    public static final ForgeConfigSpec.BooleanValue ACCEPT_OP_MUSIC;
    
    // 是否显示HUD
    public static final ForgeConfigSpec.BooleanValue SHOW_HUD;
    
    // 是否显示下载提示
    public static final ForgeConfigSpec.BooleanValue SHOW_DOWNLOAD_MESSAGES;

    static {
        BUILDER.push("Music Settings");

        VOLUME = BUILDER
                .comment("Music volume (0.0 - 1.0)")
                .defineInRange("volume", 1.0, 0.0, 1.0);

        ACCEPT_OP_MUSIC = BUILDER
                .comment("Accept music from OPs")
                .define("acceptOpMusic", true);

        SHOW_HUD = BUILDER
                .comment("Show music HUD")
                .define("showHud", true);

        SHOW_DOWNLOAD_MESSAGES = BUILDER
                .comment("Show download messages")
                .define("showDownloadMessages", false);

        BUILDER.pop();
        SPEC = BUILDER.build();
    }

    public static void register() {
        ModLoadingContext.get().registerConfig(ModConfig.Type.CLIENT, SPEC);
    }

    // 便捷方法 - 添加安全检查
    public static double getVolume() {
        try {
            return VOLUME.get();
        } catch (IllegalStateException e) {
            return 1.0; // 默认音量
        }
    }

    public static void setVolume(double volume) {
        try {
            VOLUME.set(Math.max(0.0, Math.min(1.0, volume)));
        } catch (IllegalStateException e) {
            // 配置未加载时忽略
        }
    }

    public static boolean acceptOpMusic() {
        try {
            return ACCEPT_OP_MUSIC.get();
        } catch (IllegalStateException e) {
            return true; // 默认接受
        }
    }

    public static void setAcceptOpMusic(boolean accept) {
        try {
            ACCEPT_OP_MUSIC.set(accept);
        } catch (IllegalStateException e) {
            // 配置未加载时忽略
        }
    }

    public static boolean showHud() {
        try {
            return SHOW_HUD.get();
        } catch (IllegalStateException e) {
            return true; // 默认显示
        }
    }

    public static void setShowHud(boolean show) {
        try {
            SHOW_HUD.set(show);
        } catch (IllegalStateException e) {
            // 配置未加载时忽略
        }
    }

    public static boolean showDownloadMessages() {
        try {
            return SHOW_DOWNLOAD_MESSAGES.get();
        } catch (IllegalStateException e) {
            return false; // 默认不显示
        }
    }

    public static void setShowDownloadMessages(boolean show) {
        try {
            SHOW_DOWNLOAD_MESSAGES.set(show);
        } catch (IllegalStateException e) {
            // 配置未加载时忽略
        }
    }
}
