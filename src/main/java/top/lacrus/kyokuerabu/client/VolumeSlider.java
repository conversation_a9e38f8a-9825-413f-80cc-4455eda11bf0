package top.lacrus.kyokuerabu.client;

import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.AbstractSliderButton;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public class VolumeSlider extends AbstractSliderButton {
    private final Component prefix;
    
    public VolumeSlider(int x, int y, int width, int height, Component prefix, double value) {
        super(x, y, width, height, Component.empty(), value);
        this.prefix = prefix;
        this.updateMessage();
    }
    
    @Override
    protected void updateMessage() {
        int percentage = (int) (this.value * 100);
        this.setMessage(Component.literal(this.prefix.getString() + percentage + "%"));
    }
    
    @Override
    protected void applyValue() {
        // 实时应用音量变化
        MusicConfig.setVolume(this.value);

        // 如果当前有音乐在播放，尝试调节系统音量
        if (MinecraftJLayerPlayer.isPlayerRunning()) {
            adjustSystemVolume(this.value);
        }
    }

    private void adjustSystemVolume(double volume) {
        try {
            // 尝试调节系统主音量
            javax.sound.sampled.Mixer.Info[] mixers = javax.sound.sampled.AudioSystem.getMixerInfo();
            for (javax.sound.sampled.Mixer.Info mixerInfo : mixers) {
                javax.sound.sampled.Mixer mixer = javax.sound.sampled.AudioSystem.getMixer(mixerInfo);
                if (mixer.isLineSupported(javax.sound.sampled.Port.Info.SPEAKER)) {
                    javax.sound.sampled.Port port = (javax.sound.sampled.Port) mixer.getLine(javax.sound.sampled.Port.Info.SPEAKER);
                    port.open();

                    if (port.isControlSupported(javax.sound.sampled.FloatControl.Type.VOLUME)) {
                        javax.sound.sampled.FloatControl volumeControl = (javax.sound.sampled.FloatControl) port.getControl(javax.sound.sampled.FloatControl.Type.VOLUME);
                        float newVolume = (float) volume;
                        volumeControl.setValue(Math.max(volumeControl.getMinimum(),
                                             Math.min(volumeControl.getMaximum(), newVolume)));
                    }
                    port.close();
                    break;
                }
            }
        } catch (Exception e) {
            // 音量调节失败，忽略
        }
    }
    
    public double getValue() {
        return this.value;
    }
    
    public void setValue(double value) {
        this.value = Math.max(0.0, Math.min(1.0, value));
        this.updateMessage();
    }
}
