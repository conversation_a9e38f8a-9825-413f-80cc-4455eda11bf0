package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import javazoom.jl.decoder.JavaLayerException;
import javazoom.jl.player.advanced.AdvancedPlayer;
import javazoom.jl.player.advanced.PlaybackEvent;
import javazoom.jl.player.advanced.PlaybackListener;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import org.slf4j.Logger;

import java.io.*;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;

@OnlyIn(Dist.CLIENT)
public class JLayerPlayer {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static AdvancedPlayer currentPlayer;
    private static Thread currentPlayThread;
    private static volatile boolean shouldStop = false;
    
    public static void playMusicFile(Path musicFile, String fileName) {
        try {
            LOGGER.info("========== AdvancedPlayer.playMusicFile START ==========");
            LOGGER.info("File: " + musicFile.toAbsolutePath());
            LOGGER.info("Name: " + fileName);
            LOGGER.info("File exists: " + java.nio.file.Files.exists(musicFile));
            sendMessage("§e[Kyokuerabu] JLayerPlayer.playMusicFile 开始执行");

            try {
                long fileSize = java.nio.file.Files.size(musicFile);
                LOGGER.info("File size: " + fileSize + " bytes");
                sendMessage("§e[Kyokuerabu] 文件大小: " + (fileSize / 1024 / 1024) + " MB");
            } catch (Exception e) {
                LOGGER.error("Error getting file size: " + e.getMessage());
                sendMessage("§c[Kyokuerabu] 获取文件大小失败: " + e.getMessage());
                return; // 如果无法获取文件大小，直接返回
            }

            // 停止当前播放
            LOGGER.info("Stopping current music...");
            stopCurrentMusic();
            LOGGER.info("Current music stopped");

            sendMessage("§a[Kyokuerabu] AdvancedPlayer 开始播放文件: " + fileName);
            LOGGER.info("About to start async playback...");
            sendMessage("§e[Kyokuerabu] 准备启动异步播放任务...");
        
        CompletableFuture.runAsync(() -> {
            try {
                LOGGER.info("Starting async playback task...");
                sendMessage("§e[Kyokuerabu] 正在初始化播放器...");

                LOGGER.info("Creating FileInputStream...");
                FileInputStream fis = new FileInputStream(musicFile.toFile());
                LOGGER.info("FileInputStream created successfully");

                LOGGER.info("Creating BufferedInputStream...");
                BufferedInputStream bis = new BufferedInputStream(fis);
                LOGGER.info("BufferedInputStream created successfully");

                LOGGER.info("Creating AdvancedPlayer...");
                sendMessage("§e[Kyokuerabu] 正在创建AdvancedPlayer...");
                currentPlayer = new AdvancedPlayer(bis);
                LOGGER.info("AdvancedPlayer created successfully");
                sendMessage("§a[Kyokuerabu] AdvancedPlayer创建成功");

                // 添加播放监听器
                LOGGER.info("Setting up playback listener...");
                currentPlayer.setPlayBackListener(new PlaybackListener() {
                    @Override
                    public void playbackStarted(PlaybackEvent evt) {
                        LOGGER.info("Playback started event received");
                        sendMessage("§a[Kyokuerabu] ♪ 开始播放: " + fileName);
                    }

                    @Override
                    public void playbackFinished(PlaybackEvent evt) {
                        LOGGER.info("Playback finished event received");
                        if (!shouldStop) {
                            sendMessage("§e[Kyokuerabu] ♪ 播放完成: " + fileName);
                        }
                        cleanup();
                    }
                });

                shouldStop = false;

                LOGGER.info("AdvancedPlayer initialized from file");
                sendMessage("§a[Kyokuerabu] AdvancedPlayer播放器初始化成功");
                
                LOGGER.info("Creating playback thread...");
                sendMessage("§e[Kyokuerabu] 正在启动播放线程...");
                currentPlayThread = new Thread(() -> {
                    try {
                        LOGGER.info("Playback thread started for: " + fileName);
                        LOGGER.info("Thread name: " + Thread.currentThread().getName());
                        sendMessage("§a[Kyokuerabu] 播放线程已启动");

                        LOGGER.info("Calling currentPlayer.play()...");
                        sendMessage("§a[Kyokuerabu] 正在调用播放方法...");
                        currentPlayer.play();
                        LOGGER.info("currentPlayer.play() returned");
                        sendMessage("§e[Kyokuerabu] 播放方法调用完成");

                        // AdvancedPlayer的播放完成会通过监听器处理，这里不需要额外处理
                        
                    } catch (JavaLayerException e) {
                        if (!shouldStop) {
                            LOGGER.error("AdvancedPlayer playback error: " + e.getClass().getSimpleName() + ": " + e.getMessage(), e);
                            sendMessage("§c[Kyokuerabu] 播放错误: " + e.getMessage());
                        }
                    } catch (Exception e) {
                        if (!shouldStop) {
                            LOGGER.error("Unexpected playback error: " + e.getClass().getSimpleName() + ": " + e.getMessage(), e);
                            sendMessage("§c[Kyokuerabu] 意外错误: " + e.getMessage());
                        }
                    } finally {
                        LOGGER.info("Playback thread finishing");
                        sendMessage("§e[Kyokuerabu] 播放线程结束");
                        cleanup();
                    }
                });

                LOGGER.info("Setting thread properties...");
                currentPlayThread.setDaemon(true);
                currentPlayThread.setName("AdvancedPlayer-File-" + fileName);

                LOGGER.info("Starting playback thread...");
                sendMessage("§a[Kyokuerabu] 启动播放线程...");
                currentPlayThread.start();

                LOGGER.info("AdvancedPlayer file playback thread started successfully");
                sendMessage("§a[Kyokuerabu] AdvancedPlayer文件播放线程启动成功");
                
            } catch (Exception e) {
                LOGGER.error("Error initializing AdvancedPlayer from file: " + e.getClass().getSimpleName() + ": " + e.getMessage(), e);
                sendMessage("§c[Kyokuerabu] 初始化AdvancedPlayer播放器失败: " + e.getMessage());
            }
        }).exceptionally(throwable -> {
            LOGGER.error("Async task failed: " + throwable.getMessage(), throwable);
            sendMessage("§c[Kyokuerabu] 异步播放任务失败: " + throwable.getMessage());
            return null;
        });

        } catch (Exception e) {
            LOGGER.error("Error in playMusicFile: " + e.getMessage(), e);
            sendMessage("§c[Kyokuerabu] playMusicFile执行失败: " + e.getMessage());
        }

        LOGGER.info("========== AdvancedPlayer.playMusicFile END ==========");
    }

    public static void playMusicFromMemory(String fileName, byte[] musicData) {
        LOGGER.info("========== AdvancedPlayer.playMusicFromMemory ==========");
        LOGGER.info("File: " + fileName + ", Size: " + musicData.length + " bytes");

        // 停止当前播放
        stopCurrentMusic();

        sendMessage("§a[Kyokuerabu] AdvancedPlayer 开始内存播放: " + fileName);
        sendMessage("§e[Kyokuerabu] 数据大小: " + (musicData.length / 1024 / 1024) + " MB");
        
        CompletableFuture.runAsync(() -> {
            try {
                ByteArrayInputStream bis = new ByteArrayInputStream(musicData);
                currentPlayer = new AdvancedPlayer(bis);

                // 添加播放监听器
                currentPlayer.setPlayBackListener(new PlaybackListener() {
                    @Override
                    public void playbackStarted(PlaybackEvent evt) {
                        System.out.println("[Kyokuerabu] Memory playback started event received");
                        sendMessage("§a[Kyokuerabu] 开始内存播放: " + fileName);
                    }

                    @Override
                    public void playbackFinished(PlaybackEvent evt) {
                        System.out.println("[Kyokuerabu] Memory playback finished event received");
                        if (!shouldStop) {
                            sendMessage("§e[Kyokuerabu] 内存播放完成: " + fileName);
                        }
                        cleanup();
                    }
                });

                shouldStop = false;

                System.out.println("[Kyokuerabu] AdvancedPlayer initialized from memory");
                sendMessage("§a[Kyokuerabu] AdvancedPlayer内存播放器初始化成功");
                
                currentPlayThread = new Thread(() -> {
                    try {
                        System.out.println("[Kyokuerabu] Starting AdvancedPlayer playback from memory: " + fileName);

                        currentPlayer.play();

                        // AdvancedPlayer的播放完成会通过监听器处理
                        
                    } catch (JavaLayerException e) {
                        if (!shouldStop) {
                            System.out.println("[Kyokuerabu] AdvancedPlayer memory playback error: " + e.getMessage());
                            sendMessage("§c[Kyokuerabu] 播放错误: " + e.getMessage());
                        }
                    } finally {
                        cleanup();
                    }
                });

                currentPlayThread.setDaemon(true);
                currentPlayThread.setName("AdvancedPlayer-Memory-" + fileName);
                currentPlayThread.start();

                System.out.println("[Kyokuerabu] AdvancedPlayer memory playback thread started");
                
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Error initializing AdvancedPlayer from memory: " + e.getMessage());
                e.printStackTrace();
                sendMessage("§c[Kyokuerabu] 初始化AdvancedPlayer内存播放器失败: " + e.getMessage());
            }
        });
    }
    
    public static void stopCurrentMusic() {
        shouldStop = true;

        if (currentPlayer != null) {
            try {
                currentPlayer.stop();
                currentPlayer.close();
                System.out.println("[Kyokuerabu] Stopped AdvancedPlayer");
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Error stopping AdvancedPlayer: " + e.getMessage());
            }
        }

        if (currentPlayThread != null && currentPlayThread.isAlive()) {
            try {
                currentPlayThread.interrupt();
                currentPlayThread.join(1000);
                System.out.println("[Kyokuerabu] Stopped AdvancedPlayer playback thread");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        cleanup();
    }
    
    private static void cleanup() {
        currentPlayer = null;
        currentPlayThread = null;
        shouldStop = false;
        System.out.println("[Kyokuerabu] AdvancedPlayer cleanup completed");
    }
    
    private static void sendMessage(String message) {
        try {
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(Component.literal(message));
                }
            });
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Failed to send message: " + e.getMessage());
        }
    }
}
