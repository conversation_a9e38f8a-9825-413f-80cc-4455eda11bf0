package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import org.slf4j.Logger;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@OnlyIn(Dist.CLIENT)
public class MinecraftJLayerPlayer {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static Process currentPlayerProcess;

    public static void playMusicFile(Path musicFile, String fileName) {
        try {
            if (!Files.exists(musicFile)) {
                sendMessage("§c[Kyokuerabu] 文件不存在");
                return;
            }

            // 停止当前播放
            stopCurrentMusic();

            // 启动外部播放器
            startExternalPlayer(musicFile, fileName);

        } catch (Exception e) {
            LOGGER.error("Error in playMusicFile: " + e.getMessage(), e);
            sendMessage("§c[Kyokuerabu] 播放失败: " + e.getMessage());
        }
    }

    public static void playMusicFromMemory(String fileName, byte[] musicData) {
        try {
            // 停止当前播放
            stopCurrentMusic();

            // 将内存数据保存为临时文件，然后播放
            Path tempFile = saveMemoryDataToTempFile(fileName, musicData);
            startExternalPlayer(tempFile, fileName);

        } catch (Exception e) {
            LOGGER.error("Error in playMusicFromMemory: " + e.getMessage(), e);
            sendMessage("§c[Kyokuerabu] 内存播放失败: " + e.getMessage());
        }
    }

    private static void startExternalPlayer(Path musicFile, String fileName) {
        try {
            // 获取当前Java可执行文件路径
            String javaHome = System.getProperty("java.home");
            String javaBin = javaHome + File.separator + "bin" + File.separator + "java.exe";

            // 如果是Linux/Mac，使用java而不是java.exe
            if (!Files.exists(Paths.get(javaBin))) {
                javaBin = javaHome + File.separator + "bin" + File.separator + "java";
            }

            // 获取当前classpath
            String classpath = System.getProperty("java.class.path");

            // 获取当前进程ID
            String currentPid = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];

            // 构建命令，传递当前进程ID、文件路径和音量
            ProcessBuilder pb = new ProcessBuilder(
                javaBin,
                "-cp", classpath,
                "top.lacrus.kyokuerabu.standalone.StandaloneAudioPlayer",
                musicFile.toAbsolutePath().toString(),
                currentPid,
                String.valueOf(MusicConfig.getVolume())
            );

            pb.redirectErrorStream(true);
            pb.redirectOutput(ProcessBuilder.Redirect.DISCARD);

            currentPlayerProcess = pb.start();

            // 更新HUD显示
            String displayName = fileName.replaceAll("\\.[^.]*$", ""); // 移除扩展名
            System.out.println("[Kyokuerabu] MinecraftJLayerPlayer: Updating HUD for " + displayName);
            System.out.println("[Kyokuerabu] Music file path: " + musicFile);
            System.out.println("[Kyokuerabu] Show HUD enabled: " + MusicConfig.showHud());

            if (MusicConfig.showHud()) {
                System.out.println("[Kyokuerabu] Updating HUD");
                MusicHUD.setCurrentMusic(displayName, getCurrentPlayerName());
            } else {
                System.out.println("[Kyokuerabu] HUD disabled, skipping HUD update");
            }

            sendMessage("§a[Kyokuerabu] ♪ 开始播放: " + displayName);

        } catch (Exception e) {
            LOGGER.error("Failed to start external player: " + e.getMessage(), e);
            sendMessage("§c[Kyokuerabu] 启动播放器失败: " + e.getMessage());
        }
    }

    private static Path saveMemoryDataToTempFile(String fileName, byte[] musicData) throws IOException {
        Path tempDir = Files.createTempDirectory("kyokuerabu_music");
        Path tempFile = tempDir.resolve(fileName);
        Files.write(tempFile, musicData);
        return tempFile;
    }

    public static void stopCurrentMusic() {
        try {
            if (currentPlayerProcess != null && currentPlayerProcess.isAlive()) {
                currentPlayerProcess.destroyForcibly();
                LOGGER.info("Stopped external player process");
            }

            // 清除HUD显示
            MusicHUD.clearCurrentMusic();

        } catch (Exception e) {
            LOGGER.error("Error stopping current music: " + e.getMessage(), e);
        }
    }

    private static String getCurrentPlayerName() {
        try {
            Minecraft mc = Minecraft.getInstance();
            if (mc.player != null) {
                return mc.player.getName().getString();
            }
        } catch (Exception e) {
            LOGGER.warn("Failed to get player name: " + e.getMessage());
        }
        return "Unknown";
    }

    private static void sendMessage(String message) {
        try {
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(Component.literal(message));
                }
            });
        } catch (Exception e) {
            LOGGER.error("Failed to send message: " + e.getMessage());
        }
    }

    // 检查播放器进程是否还在运行
    public static boolean isPlayerRunning() {
        return currentPlayerProcess != null && currentPlayerProcess.isAlive();
    }

    // 游戏退出时清理
    public static void onGameExit() {
        stopCurrentMusic();
    }
}
