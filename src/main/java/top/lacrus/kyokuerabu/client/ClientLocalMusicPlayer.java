package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import org.slf4j.Logger;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;

@OnlyIn(Dist.CLIENT)
public class ClientLocalMusicPlayer {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static Process currentMusicProcess;
    private static volatile boolean shouldStop = false;
    
    public static void playLocalMusic(String fileName, String serverMusicPath) {
        System.out.println("[Kyokuerabu] ClientLocalMusicPlayer.playLocalMusic called with file: " + fileName);
        LOGGER.info("ClientLocalMusicPlayer.playLocalMusic called with file: {}", fileName);

        // 向玩家发送开始消息
        if (Minecraft.getInstance().player != null) {
            Minecraft.getInstance().player.sendSystemMessage(
                Component.literal("§a[Kyokuerabu] 开始播放本地音乐: " + fileName)
            );
        }

        // 停止当前播放的音乐
        stopCurrentMusic();

        // 通知玩家等待服务器传输音乐文件
        if (Minecraft.getInstance().player != null) {
            Minecraft.getInstance().player.sendSystemMessage(
                Component.literal("§e[Kyokuerabu] 等待服务器传输音乐文件...")
            );
        }
    }
    

    
    public static void stopCurrentMusic() {
        shouldStop = true;

        // 停止MinecraftJLayerPlayer音乐播放器
        MinecraftJLayerPlayer.stopCurrentMusic();

        if (currentMusicProcess != null && currentMusicProcess.isAlive()) {
            try {
                currentMusicProcess.destroyForcibly();
                System.out.println("[Kyokuerabu] Stopped current local music process");
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Error stopping local music process: " + e.getMessage());
            }
        }

        cleanup();
    }

    private static void cleanup() {
        currentMusicProcess = null;
        shouldStop = false;
        System.out.println("[Kyokuerabu] Local music player cleanup completed");
    }
}
