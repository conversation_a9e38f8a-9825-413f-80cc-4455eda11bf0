package top.lacrus.kyokuerabu.client;

import com.mojang.blaze3d.platform.InputConstants;
import net.minecraft.client.KeyMapping;
import net.minecraft.client.Minecraft;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.InputEvent;
import net.minecraftforge.client.event.RegisterKeyMappingsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import org.lwjgl.glfw.GLFW;

@Mod.EventBusSubscriber(modid = "kyokuerabu", value = Dist.CLIENT, bus = Mod.EventBusSubscriber.Bus.MOD)
public class KeyBindings {
    
    public static final KeyMapping OPEN_MUSIC_CONFIG = new KeyMapping(
        "key.kyokuerabu.open_music_config",
        InputConstants.Type.KEYSYM,
        GLFW.GLFW_KEY_M,
        "key.categories.kyokuerabu"
    );
    
    public static final KeyMapping TOGGLE_HUD = new KeyMapping(
        "key.kyokuerabu.toggle_hud",
        InputConstants.Type.KEYSYM,
        GLFW.GLFW_KEY_H,
        "key.categories.kyokuerabu"
    );
    
    public static final KeyMapping STOP_MUSIC = new KeyMapping(
        "key.kyokuerabu.stop_music",
        InputConstants.Type.KEYSYM,
        GLFW.GLFW_KEY_X,
        "key.categories.kyokuerabu"
    );
    
    @SubscribeEvent
    public static void registerKeyMappings(RegisterKeyMappingsEvent event) {
        event.register(OPEN_MUSIC_CONFIG);
        event.register(TOGGLE_HUD);
        event.register(STOP_MUSIC);
    }
    

}
