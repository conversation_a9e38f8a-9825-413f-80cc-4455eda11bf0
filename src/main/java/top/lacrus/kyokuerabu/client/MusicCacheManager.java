package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.network.PacketDistributor;
import org.slf4j.Logger;
import top.lacrus.kyokuerabu.network.MusicCacheResponsePacket;
import top.lacrus.kyokuerabu.network.NetworkHandler;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.concurrent.CompletableFuture;

@OnlyIn(Dist.CLIENT)
public class MusicCacheManager {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static final Path CLIENT_MUSIC_DIR = Paths.get("client_music");
    
    public static void handleFileInfo(String fileName, String serverHash, long serverFileSize) {
        LOGGER.info("MusicCacheManager.handleFileInfo called");
        LOGGER.info("File: " + fileName);
        LOGGER.info("Server hash: " + serverHash);
        LOGGER.info("Server file size: " + serverFileSize);

        CompletableFuture.runAsync(() -> {
            try {
                // 检查本地文件
                Path localFile = CLIENT_MUSIC_DIR.resolve(fileName);
                boolean needDownload = true;
                String reason = "";
                
                if (!Files.exists(CLIENT_MUSIC_DIR)) {
                    Files.createDirectories(CLIENT_MUSIC_DIR);
                    reason = "客户端音乐目录不存在，已创建";
                    LOGGER.info("Created client_music directory");
                } else if (!Files.exists(localFile)) {
                    LOGGER.info("Local file does not exist, starting multi-thread download");
                    // 启动多线程下载
                    startMultiThreadDownload(fileName, serverHash, serverFileSize);

                    needDownload = false; // 不需要服务器发送文件
                    reason = "多线程下载中";
                } else {
                    // 文件存在，检查哈希
                    LOGGER.info("Local file exists, checking hash...");

                    try {
                        long localFileSize = Files.size(localFile);
                        LOGGER.info("Local file size: " + localFileSize);

                        if (localFileSize != serverFileSize) {
                            reason = "文件大小不匹配 (本地: " + localFileSize + ", 服务器: " + serverFileSize + ")";
                            LOGGER.info("File size mismatch: " + reason);
                        } else {
                            String localHash = calculateFileHash(localFile);
                            LOGGER.info("Local hash: " + localHash);
                            LOGGER.info("Server hash: " + serverHash);

                            if (serverHash.equals(localHash)) {
                                needDownload = false;
                                reason = "哈希匹配，直接播放本地文件";
                                LOGGER.info("Hash matches, playing local file directly");
                                if (MusicConfig.showDownloadMessages()) {}

                                // 直接播放client_music中的文件
                                playMusicFromClientMusic(fileName);
                            } else {
                                LOGGER.info("Hash mismatch, starting multi-thread download");
                                sendMessage("§e[Kyokuerabu] 哈希不匹配，启动多线程下载");

                                // 启动多线程下载
                                startMultiThreadDownload(fileName, serverHash, serverFileSize);

                                needDownload = false;
                                reason = "多线程下载中";
                            }
                        }
                    } catch (Exception e) {
                        reason = "验证文件时出错: " + e.getMessage();
                        LOGGER.error("Error verifying file: " + e.getMessage(), e);
                    }
                }
                
                // 发送响应给服务器
                LOGGER.info("Sending cache response: needDownload=" + needDownload + ", reason=" + reason);
                sendCacheResponse(fileName, needDownload, reason);

                if (needDownload) {
                    sendMessage("§e[Kyokuerabu] " + reason + "，等待服务器传输...");
                }

            } catch (Exception e) {
                LOGGER.error("Error in handleFileInfo: " + e.getMessage(), e);
                sendCacheResponse(fileName, true, "处理文件信息时出错: " + e.getMessage());
            }
        });
    }
    
    public static String calculateFileHash(Path file) throws Exception {
        LOGGER.info("Calculating hash for: " + file);

        byte[] fileBytes = Files.readAllBytes(file);
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = md.digest(fileBytes);

        StringBuilder sb = new StringBuilder();
        for (byte b : hashBytes) {
            sb.append(String.format("%02x", b));
        }

        String hash = sb.toString();
        LOGGER.info("Calculated hash: " + hash);
        return hash;
    }
    
    private static void sendCacheResponse(String fileName, boolean needDownload, String reason) {
        try {
            MusicCacheResponsePacket response = new MusicCacheResponsePacket(fileName, needDownload, reason);
            NetworkHandler.INSTANCE.sendToServer(response);
            LOGGER.info("Sent cache response to server");
        } catch (Exception e) {
            LOGGER.error("Error sending cache response: " + e.getMessage(), e);
        }
    }
    
    private static void playMusicFromClientMusic(String fileName) {
        try {
            Path musicFile = CLIENT_MUSIC_DIR.resolve(fileName);

            LOGGER.info("========== Playing from client_music ==========");
            LOGGER.info("File: " + musicFile.toAbsolutePath());
            LOGGER.info("File exists: " + Files.exists(musicFile));

            if (!Files.exists(musicFile)) {
                LOGGER.error("File does not exist: " + musicFile);
                sendMessage("§c[Kyokuerabu] 文件不存在: " + musicFile);
                return;
            }

            long fileSize = Files.size(musicFile);
            LOGGER.info("File size: " + fileSize + " bytes");
            // 不再显示"开始播放client_music中的文件"消息，由MinecraftJLayerPlayer统一处理
            // 使用MinecraftJLayerPlayer播放文件
            LOGGER.info("Calling MinecraftJLayerPlayer.playMusicFile...");
            MinecraftJLayerPlayer.playMusicFile(musicFile, fileName);
            LOGGER.info("MinecraftJLayerPlayer.playMusicFile call completed");

        } catch (Exception e) {
            LOGGER.error("Error playing from client_music: " + e.getMessage(), e);
            sendMessage("§c[Kyokuerabu] 播放client_music文件失败: " + e.getMessage());
        }
    }
    
    private static void sendMessage(String message) {
        try {
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(Component.literal(message));
                }
            });
        } catch (Exception e) {
            LOGGER.error("Failed to send message: " + e.getMessage(), e);
        }
    }

    private static void startMultiThreadDownload(String fileName, String fileHash, long fileSize) {
        try {
            Path musicFile = CLIENT_MUSIC_DIR.resolve(fileName);

            LOGGER.info("Starting segmented download for: {}", fileName);
            sendMessage("§e[Kyokuerabu] 开始分段下载: " + fileName);

            // 使用分段下载器
            MultiThreadDownloader.downloadFile(
                fileName,
                fileSize,
                fileHash,
                musicFile,
                new MultiThreadDownloader.ProgressCallback() {
                    private int lastReportedPercent = -1;

                    @Override
                    public void onProgress(MultiThreadDownloader.DownloadProgress progress) {
                        // HUD会自动更新，这里只在特定百分比显示聊天消息
                        int percent = (int)(progress.getProgress() * 100);
                        if (percent >= lastReportedPercent + 25) { // 每25%显示一次
                            lastReportedPercent = percent;
                            if (MusicConfig.showDownloadMessages()) {
                                sendMessage(String.format("§e[Kyokuerabu] 下载进度: %d%%", percent));
                            }
                        }
                    }

                    @Override
                    public void onCompleted(Path filePath) {
                        sendMessage("§a[Kyokuerabu] 分段下载完成");
                    }

                    @Override
                    public void onError(Exception error) {
                        LOGGER.error("Download failed: {}", error.getMessage());
                        sendMessage("§c[Kyokuerabu] 下载失败: " + error.getMessage());
                    }
                }
            );

        } catch (Exception e) {
            LOGGER.error("Failed to start download: {}", e.getMessage());
            sendMessage("§c[Kyokuerabu] 启动下载失败: " + e.getMessage());
        }
    }

    private static void verifyAndPlay(Path musicFile, String fileName, String expectedHash) {
        try {
            // 验证文件哈希
            if (verifyFileHash(musicFile, expectedHash)) {
                LOGGER.info("File hash verified, starting playback");

                // 启动播放
                String displayName = fileName.replaceAll("\\.[^.]*$", "");
                if (MusicConfig.showHud()) {
                    MusicHUD.setCurrentMusic(displayName, getCurrentPlayerName());
                }

                // 使用MinecraftJLayerPlayer播放
                MinecraftJLayerPlayer.playMusicFile(musicFile, fileName);

            } else {
                LOGGER.error("File hash verification failed");
                Files.deleteIfExists(musicFile);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to verify and play: {}", e.getMessage());
        }
    }

    private static boolean verifyFileHash(Path filePath, String expectedHash) {
        try {
            return expectedHash.equals(calculateFileHash(filePath));
        } catch (Exception e) {
            LOGGER.error("Failed to verify hash: {}", e.getMessage());
            return false;
        }
    }

    private static String getCurrentPlayerName() {
        try {
            Minecraft mc = Minecraft.getInstance();
            if (mc.player != null) {
                return mc.player.getName().getString();
            }
        } catch (Exception e) {
            LOGGER.warn("Failed to get player name: {}", e.getMessage());
        }
        return "Unknown";
    }
}
