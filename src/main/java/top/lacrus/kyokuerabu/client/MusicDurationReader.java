package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
// JLayer依赖在打包时可能缺失，移除直接依赖
import javazoom.jl.decoder.Bitstream;
import javazoom.jl.decoder.Header;
import org.slf4j.Logger;

import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.UnsupportedAudioFileException;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Map;

/**
 * 音乐时长读取工具
 * 支持MP3, OGG, WAV等格式
 */
public class MusicDurationReader {
    private static final Logger LOGGER = LogUtils.getLogger();

    /**
     * 获取音乐文件的时长（毫秒）
     */
    public static long getDurationMillis(Path musicFile) {
        if (!Files.exists(musicFile)) {
            LOGGER.warn("Music file does not exist: {}", musicFile);
            return 0;
        }

        String fileName = musicFile.getFileName().toString().toLowerCase();
        LOGGER.info("Getting duration for file: {} ({})", fileName, musicFile);

        try {
            long duration = 0;

            if (fileName.endsWith(".mp3")) {
                LOGGER.debug("Trying MP3 duration reader for: {}", fileName);
                duration = getMp3Duration(musicFile);
                if (duration > 0) {
                    LOGGER.info("Successfully got MP3 duration: {}ms ({}s)", duration, duration/1000);
                    return duration;
                }
            }

            if (fileName.endsWith(".ogg") || fileName.endsWith(".wav") || fileName.endsWith(".flac") || fileName.endsWith(".mp3")) {
                LOGGER.debug("Trying AudioSystem duration reader for: {}", fileName);
                duration = getAudioSystemDuration(musicFile);
                if (duration > 0) {
                    LOGGER.info("Successfully got AudioSystem duration: {}ms ({}s)", duration, duration/1000);
                    return duration;
                }
            }

            // 如果上述方法都失败，尝试所有方法
            LOGGER.debug("Primary methods failed, trying all methods for: {}", fileName);

            if (!fileName.endsWith(".mp3")) {
                duration = getMp3Duration(musicFile);
                if (duration > 0) {
                    LOGGER.info("Got duration via MP3 fallback: {}ms ({}s)", duration, duration/1000);
                    return duration;
                }
            }

            if (!fileName.endsWith(".ogg") && !fileName.endsWith(".wav") && !fileName.endsWith(".flac")) {
                duration = getAudioSystemDuration(musicFile);
                if (duration > 0) {
                    LOGGER.info("Got duration via AudioSystem fallback: {}ms ({}s)", duration, duration/1000);
                    return duration;
                }
            }

            // 最后使用文件大小估算
            LOGGER.warn("All duration readers failed, using file size estimation for: {}", fileName);
            duration = estimateDurationFromFileSize(musicFile);
            LOGGER.info("Estimated duration from file size: {}ms ({}s)", duration, duration/1000);
            return duration;

        } catch (Exception e) {
            LOGGER.error("Exception while getting duration for {}: {}", musicFile, e.getMessage(), e);
            long duration = estimateDurationFromFileSize(musicFile);
            LOGGER.info("Fallback estimated duration: {}ms ({}s)", duration, duration/1000);
            return duration;
        }
    }

    /**
     * 获取MP3文件时长
     */
    private static long getMp3Duration(Path musicFile) {
        try (FileInputStream fis = new FileInputStream(musicFile.toFile());
             BufferedInputStream bis = new BufferedInputStream(fis)) {

            Bitstream bitstream = null;
            try {
                bitstream = new Bitstream(bis);
                Header header = bitstream.readFrame();

                if (header == null) {
                    LOGGER.debug("No MP3 header found in file: {}", musicFile);
                    return 0;
                }

                long fileSize = Files.size(musicFile);
                int bitrate = header.bitrate();

                if (bitrate > 0) {
                    // 计算时长：文件大小(字节) * 8 / 比特率(bps) * 1000 = 毫秒
                    long durationMs = (fileSize * 8 * 1000) / bitrate;
                    LOGGER.info("MP3 duration calculated: {}ms ({}s) (bitrate: {}bps, size: {}bytes)",
                               durationMs, durationMs/1000, bitrate, fileSize);
                    return durationMs;
                } else {
                    LOGGER.debug("Invalid bitrate in MP3 file: {}", bitrate);
                }
            } finally {
                if (bitstream != null) {
                    try {
                        bitstream.close();
                    } catch (Exception e) {
                        LOGGER.debug("Error closing bitstream: {}", e.getMessage());
                    }
                }
            }

        } catch (Exception e) {
            LOGGER.debug("Failed to read MP3 duration: {}", e.getMessage());
        }

        return 0;
    }

    /**
     * 使用AudioSystem获取音频文件时长
     */
    private static long getAudioSystemDuration(Path musicFile) {
        try {
            AudioFileFormat fileFormat = AudioSystem.getAudioFileFormat(musicFile.toFile());
            
            // 尝试从属性中获取时长
            Map<String, Object> properties = fileFormat.properties();
            if (properties != null) {
                // 检查duration属性（微秒）
                Object duration = properties.get("duration");
                if (duration instanceof Long) {
                    long durationMicros = (Long) duration;
                    long durationMs = durationMicros / 1000;
                    LOGGER.info("Audio duration from properties: {}ms ({}s)", durationMs, durationMs/1000);
                    return durationMs;
                }
                
                // 检查其他可能的时长属性
                Object author = properties.get("author");
                Object title = properties.get("title");
                LOGGER.debug("Audio properties: {}", properties);
            }

            // 尝试通过帧数计算
            if (fileFormat.getFrameLength() != AudioSystem.NOT_SPECIFIED) {
                float frameRate = fileFormat.getFormat().getFrameRate();
                if (frameRate > 0) {
                    long durationMs = (long) ((fileFormat.getFrameLength() / frameRate) * 1000);
                    LOGGER.info("Audio duration from frames: {}ms ({}s) (frames: {}, rate: {})",
                               durationMs, durationMs/1000, fileFormat.getFrameLength(), frameRate);
                    return durationMs;
                } else {
                    LOGGER.debug("Invalid frame rate: {}", frameRate);
                }
            } else {
                LOGGER.debug("Frame length not specified for file: {}", musicFile);
            }

        } catch (UnsupportedAudioFileException e) {
            LOGGER.debug("Unsupported audio file format: {}", e.getMessage());
        } catch (Exception e) {
            LOGGER.debug("Failed to read audio duration: {}", e.getMessage());
        }
        
        return 0;
    }

    /**
     * 基于文件大小估算时长
     */
    private static long estimateDurationFromFileSize(Path musicFile) {
        try {
            long fileSize = Files.size(musicFile);
            String fileName = musicFile.getFileName().toString().toLowerCase();
            
            // 根据文件格式使用不同的估算比率
            long estimatedMs;
            if (fileName.endsWith(".mp3")) {
                // MP3: 假设128kbps平均比特率
                // 1MB ≈ 64秒 (128kbps)
                estimatedMs = (fileSize * 64 * 1000) / (1024 * 1024);
            } else if (fileName.endsWith(".ogg")) {
                // OGG: 假设96kbps平均比特率
                // 1MB ≈ 85秒 (96kbps)
                estimatedMs = (fileSize * 85 * 1000) / (1024 * 1024);
            } else if (fileName.endsWith(".wav")) {
                // WAV: 假设1411kbps (CD质量)
                // 1MB ≈ 5.8秒
                estimatedMs = (fileSize * 6 * 1000) / (1024 * 1024);
            } else {
                // 默认假设128kbps
                estimatedMs = (fileSize * 64 * 1000) / (1024 * 1024);
            }
            
            LOGGER.debug("Estimated duration from file size: {}ms (size: {}bytes)", estimatedMs, fileSize);
            return estimatedMs;
            
        } catch (Exception e) {
            LOGGER.warn("Failed to estimate duration: {}", e.getMessage());
            return 3 * 60 * 1000; // 默认3分钟
        }
    }

    /**
     * 格式化时长为可读字符串
     */
    public static String formatDuration(long durationMs) {
        if (durationMs <= 0) {
            return "00:00";
        }
        
        long seconds = durationMs / 1000;
        long minutes = seconds / 60;
        long remainingSeconds = seconds % 60;
        
        if (minutes >= 60) {
            long hours = minutes / 60;
            long remainingMinutes = minutes % 60;
            return String.format("%d:%02d:%02d", hours, remainingMinutes, remainingSeconds);
        } else {
            return String.format("%02d:%02d", minutes, remainingSeconds);
        }
    }

    /**
     * 异步获取音乐时长
     */
    public static void getDurationAsync(Path musicFile, DurationCallback callback) {
        LOGGER.info("Starting async duration calculation for: {}", musicFile);
        java.util.concurrent.CompletableFuture.supplyAsync(() -> {
            LOGGER.debug("Async task started for duration calculation");
            long duration = getDurationMillis(musicFile);
            LOGGER.info("Async duration calculation completed: {}ms ({}s)", duration, duration/1000);
            return duration;
        })
        .thenAccept(duration -> {
            LOGGER.debug("Calling duration callback with result: {}ms", duration);
            if (callback != null) {
                callback.onDurationReceived(duration);
            } else {
                LOGGER.warn("Duration callback is null!");
            }
        })
        .exceptionally(throwable -> {
            LOGGER.error("Failed to get duration async: {}", throwable.getMessage(), throwable);
            if (callback != null) {
                callback.onDurationReceived(0);
            }
            return null;
        });
    }

    public interface DurationCallback {
        void onDurationReceived(long durationMs);
    }
}
