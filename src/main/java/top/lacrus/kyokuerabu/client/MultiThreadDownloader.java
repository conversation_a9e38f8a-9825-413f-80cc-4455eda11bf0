package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import org.slf4j.Logger;
import top.lacrus.kyokuerabu.network.MusicSegmentRequestPacket;
import top.lacrus.kyokuerabu.network.NetworkHandler;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 多线程分块下载器
 */
public class MultiThreadDownloader {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static final int THREAD_COUNT = 4;
    private static final long MIN_CHUNK_SIZE = 512 * 1024; // 512KB最小分块大小
    private static final long MAX_SEGMENT_SIZE = 800 * 1024; // 800KB最大分段大小（留出缓冲）
    private static final int CONNECT_TIMEOUT = 10000; // 10秒
    private static final int READ_TIMEOUT = 30000; // 30秒

    public static class DownloadProgress {
        private final AtomicLong downloaded = new AtomicLong(0);
        private final long totalSize;
        private volatile boolean completed = false;
        private volatile String error = null;

        public DownloadProgress(long totalSize) {
            this.totalSize = totalSize;
        }

        public long getDownloaded() { return downloaded.get(); }
        public long getTotalSize() { return totalSize; }
        public double getProgress() { return totalSize > 0 ? (double) downloaded.get() / totalSize : 0; }
        public boolean isCompleted() { return completed; }
        public String getError() { return error; }

        void addDownloaded(long bytes) { downloaded.addAndGet(bytes); }
        void setCompleted() { completed = true; }
        void setError(String error) { this.error = error; }
    }

    public static DownloadProgress downloadFile(String fileName, long fileSize, String expectedHash, Path outputPath, ProgressCallback callback) {
        try {
            LOGGER.info("Starting segmented download: {} ({} bytes)", fileName, fileSize);

            DownloadProgress progress = new DownloadProgress(fileSize);

            // 计算分段数量
            int totalSegments = calculateSegmentCount(fileSize);

            // 启动分段下载
            CompletableFuture.runAsync(() -> downloadWithSegments(fileName, fileSize, expectedHash, totalSegments, outputPath, progress, callback));

            return progress;

        } catch (Exception e) {
            LOGGER.error("Failed to start download: {}", e.getMessage());
            DownloadProgress progress = new DownloadProgress(0);
            progress.setError(e.getMessage());
            return progress;
        }
    }

    private static int calculateSegmentCount(long fileSize) {
        if (fileSize <= MIN_CHUNK_SIZE) {
            return 1; // 小文件使用单个分段
        }

        // 计算分段数量，确保每个分段不超过800KB
        int segments = (int) Math.ceil((double) fileSize / MAX_SEGMENT_SIZE);

        // 限制最大分段数量，避免过多的网络请求
        int maxSegments = Math.max(8, (int) Math.ceil((double) fileSize / (100 * 1024))); // 至少100KB per segment

        return Math.min(segments, maxSegments);
    }

    private static void downloadWithSegments(String fileName, long fileSize, String expectedHash, int totalSegments,
                                           Path outputPath, DownloadProgress progress, ProgressCallback callback) {
        try {
            // 启动分段接收器
            MusicSegmentReceiver.startDownload(fileName, fileSize, expectedHash, totalSegments);

            // 请求所有分段
            requestAllSegments(fileName, fileSize, totalSegments, progress, callback);

        } catch (Exception e) {
            LOGGER.error("Segmented download failed: {}", e.getMessage());
            progress.setError(e.getMessage());
            if (callback != null) {
                callback.onError(e);
            }
        }
    }

    private static void requestAllSegments(String fileName, long fileSize, int totalSegments,
                                         DownloadProgress progress, ProgressCallback callback) {
        try {
            for (int i = 0; i < totalSegments; i++) {
                long startByte = i * MAX_SEGMENT_SIZE;
                long endByte = Math.min(startByte + MAX_SEGMENT_SIZE - 1, fileSize - 1);

                // 验证分段大小
                long actualSegmentSize = endByte - startByte + 1;
                if (actualSegmentSize > MAX_SEGMENT_SIZE) {
                    LOGGER.error("Segment {} too large: {} bytes (max: {})", i, actualSegmentSize, MAX_SEGMENT_SIZE);
                    progress.setError("Segment too large: " + actualSegmentSize + " bytes");
                    if (callback != null) {
                        callback.onError(new IllegalArgumentException("Segment too large"));
                    }
                    return;
                }

                // 发送分段请求
                MusicSegmentRequestPacket requestPacket = new MusicSegmentRequestPacket(
                    fileName, i, totalSegments, startByte, endByte
                );

                NetworkHandler.INSTANCE.sendToServer(requestPacket);

                LOGGER.debug("Requested segment {}/{}: {}-{} ({} bytes)",
                           i + 1, totalSegments, startByte, endByte, actualSegmentSize);

                // 添加小延迟避免网络拥塞
                if (i < totalSegments - 1) {
                    Thread.sleep(50);
                }
            }

            LOGGER.info("Requested all {} segments for {} (max segment size: {} KB)",
                       totalSegments, fileName, MAX_SEGMENT_SIZE / 1024);

        } catch (Exception e) {
            LOGGER.error("Failed to request segments: {}", e.getMessage());
            progress.setError(e.getMessage());
            if (callback != null) {
                callback.onError(e);
            }
        }
    }

    // HTTP下载方法已移除，使用数据包通信

    public interface ProgressCallback {
        void onProgress(DownloadProgress progress);
        void onCompleted(Path filePath);
        void onError(Exception error);
    }
}
