package top.lacrus.kyokuerabu.server;

import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.PacketDistributor;
import top.lacrus.kyokuerabu.network.MusicSegmentResponsePacket;
import top.lacrus.kyokuerabu.network.NetworkHandler;

import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 服务器端音乐分段处理器
 */
public class MusicSegmentHandler {
    private static final Path MUSIC_DIR = Paths.get("music");
    private static final long MAX_SEGMENT_SIZE = 800 * 1024; // 800KB最大分段大小

    public static void handleSegmentRequest(String fileName, int segmentIndex, int totalSegments,
                                          long startByte, long endByte, ServerPlayer player) {
        try {
            // 查找音乐文件
            Path musicFile = findMusicFile(fileName);
            if (musicFile == null || !Files.exists(musicFile)) {
                System.err.println("[Kyokuerabu] Music file not found: " + fileName);
                return;
            }

            // 验证分段大小
            long segmentSize = endByte - startByte + 1;
            if (segmentSize > MAX_SEGMENT_SIZE) {
                System.err.println("[Kyokuerabu] Segment too large: " + segmentSize + " bytes (max: " + MAX_SEGMENT_SIZE + ")");
                return;
            }

            // 读取指定分段的数据
            byte[] segmentData = readFileSegment(musicFile, startByte, endByte);
            if (segmentData == null) {
                System.err.println("[Kyokuerabu] Failed to read segment: " + fileName);
                return;
            }

            // 创建响应包
            boolean isLastSegment = (segmentIndex == totalSegments - 1);
            MusicSegmentResponsePacket responsePacket = new MusicSegmentResponsePacket(
                fileName, segmentIndex, totalSegments, startByte, endByte, segmentData, isLastSegment
            );

            // 发送给客户端
            NetworkHandler.INSTANCE.send(
                PacketDistributor.PLAYER.with(() -> player),
                responsePacket
            );

            System.out.println("[Kyokuerabu] Sent segment " + segmentIndex + "/" + totalSegments +
                             " to " + player.getName().getString() + " (" + segmentData.length + " bytes, " +
                             segmentData.length / 1024 + " KB)");

        } catch (Exception e) {
            System.err.println("[Kyokuerabu] Error handling segment request: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static Path findMusicFile(String fileName) {
        if (!Files.exists(MUSIC_DIR)) {
            return null;
        }

        // 尝试直接匹配
        Path directMatch = MUSIC_DIR.resolve(fileName);
        if (Files.exists(directMatch)) {
            return directMatch;
        }

        // 如果没有扩展名，尝试添加常见的音频扩展名
        if (!fileName.contains(".")) {
            String[] extensions = {".mp3", ".ogg", ".wav", ".flac"};
            for (String ext : extensions) {
                Path fileWithExt = MUSIC_DIR.resolve(fileName + ext);
                if (Files.exists(fileWithExt)) {
                    return fileWithExt;
                }
            }
        }

        return null;
    }

    private static byte[] readFileSegment(Path filePath, long startByte, long endByte) {
        try (RandomAccessFile file = new RandomAccessFile(filePath.toFile(), "r")) {
            // 验证范围
            long fileSize = file.length();
            if (startByte < 0 || endByte >= fileSize || startByte > endByte) {
                System.err.println("[Kyokuerabu] Invalid byte range: " + startByte + "-" + endByte + 
                                 " (file size: " + fileSize + ")");
                return null;
            }

            // 读取分段数据
            long segmentSize = endByte - startByte + 1;
            if (segmentSize > Integer.MAX_VALUE) {
                System.err.println("[Kyokuerabu] Segment too large: " + segmentSize);
                return null;
            }

            byte[] segmentData = new byte[(int) segmentSize];
            file.seek(startByte);
            int bytesRead = file.read(segmentData);

            if (bytesRead != segmentSize) {
                System.err.println("[Kyokuerabu] Read incomplete: " + bytesRead + "/" + segmentSize);
                return null;
            }

            return segmentData;

        } catch (IOException e) {
            System.err.println("[Kyokuerabu] Error reading file segment: " + e.getMessage());
            return null;
        }
    }

    public static long getFileSize(String fileName) {
        try {
            Path musicFile = findMusicFile(fileName);
            if (musicFile != null && Files.exists(musicFile)) {
                return Files.size(musicFile);
            }
        } catch (Exception e) {
            System.err.println("[Kyokuerabu] Error getting file size: " + e.getMessage());
        }
        return -1;
    }
}
