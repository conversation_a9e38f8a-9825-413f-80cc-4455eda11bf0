package top.lacrus.kyokuerabu.server;

import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.PacketDistributor;
import top.lacrus.kyokuerabu.network.NetworkHandler;
import top.lacrus.kyokuerabu.network.PlayLocalMusicChunkPacket;
import top.lacrus.kyokuerabu.network.SimpleMusicPacket;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.MessageDigest;
import java.util.Collection;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

public class ServerMusicCacheManager {
    // 存储等待缓存响应的请求
    private static final ConcurrentHashMap<String, PendingMusicRequest> pendingRequests = new ConcurrentHashMap<>();
    
    public static void requestMusicPlay(Collection<ServerPlayer> targets, String fileName, Path musicFilePath) {
        System.out.println("[Kyokuerabu] ServerMusicCacheManager.requestMusicPlay called");
        System.out.println("[Kyokuerabu] File: " + fileName);
        System.out.println("[Kyokuerabu] Targets: " + targets.size() + " players");
        
        try {
            // 读取文件并计算哈希
            byte[] musicData = Files.readAllBytes(musicFilePath);
            long fileSize = musicData.length;
            String fileHash = calculateFileHash(musicData);
            
            System.out.println("[Kyokuerabu] File size: " + fileSize + " bytes");
            System.out.println("[Kyokuerabu] File hash: " + fileHash);
            
            // 创建待处理请求
            PendingMusicRequest request = new PendingMusicRequest(fileName, musicData, targets, fileHash, fileSize);
            
            // 为每个玩家发送文件信息
            for (ServerPlayer player : targets) {
                String requestKey = player.getUUID().toString() + ":" + fileName;
                pendingRequests.put(requestKey, request);
                
                // 发送文件信息包
                top.lacrus.kyokuerabu.network.MusicFileInfoPacket infoPacket = 
                    new top.lacrus.kyokuerabu.network.MusicFileInfoPacket(fileName, fileHash, fileSize);
                
                NetworkHandler.INSTANCE.send(
                    PacketDistributor.PLAYER.with(() -> player),
                    infoPacket
                );
                
                System.out.println("[Kyokuerabu] Sent file info to player: " + player.getName().getString());
            }
            
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error in requestMusicPlay: " + e.getMessage());
            e.printStackTrace();
            
            // 发送错误消息给所有目标玩家
            for (ServerPlayer player : targets) {
                player.sendSystemMessage(Component.literal("§c[Kyokuerabu] 服务器处理音乐文件时出错: " + e.getMessage()));
            }
        }
    }
    
    public static void handleCacheResponse(ServerPlayer player, String fileName, boolean needDownload, String reason) {
        System.out.println("[Kyokuerabu] handleCacheResponse called");
        System.out.println("[Kyokuerabu] Player: " + player.getName().getString());
        System.out.println("[Kyokuerabu] File: " + fileName);
        System.out.println("[Kyokuerabu] Need download: " + needDownload);
        System.out.println("[Kyokuerabu] Reason: " + reason);
        
        String requestKey = player.getUUID().toString() + ":" + fileName;
        PendingMusicRequest request = pendingRequests.get(requestKey);
        
        if (request == null) {
            System.out.println("[Kyokuerabu] No pending request found for: " + requestKey);
            player.sendSystemMessage(Component.literal("§c[Kyokuerabu] 未找到对应的音乐请求"));
            return;
        }
        
        // 移除待处理请求
        pendingRequests.remove(requestKey);
        
        if (needDownload) {
            System.out.println("[Kyokuerabu] Client needs download, sending music data...");
            player.sendSystemMessage(Component.literal("§e[Kyokuerabu] " + reason + "，开始传输音乐文件..."));
            
            // 发送音乐数据
            sendMusicData(player, fileName, request.musicData);
        } else {
            System.out.println("[Kyokuerabu] Client using local cache: " + reason);
            player.sendSystemMessage(Component.literal("§a[Kyokuerabu] " + reason));
        }
    }
    
    private static void sendMusicData(ServerPlayer player, String fileName, byte[] musicData) {
        CompletableFuture.runAsync(() -> {
            try {
                if (musicData.length <= 1024 * 1024) { // 1MB以下使用简单包
                    System.out.println("[Kyokuerabu] Sending small file as simple packet: " + fileName);
                    SimpleMusicPacket packet = new SimpleMusicPacket(fileName, musicData);
                    NetworkHandler.INSTANCE.send(
                        PacketDistributor.PLAYER.with(() -> player),
                        packet
                    );
                } else { // 大文件使用分块传输
                    System.out.println("[Kyokuerabu] Sending large file in chunks: " + fileName);
                    sendMusicInChunks(player, fileName, musicData);
                }
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Error sending music data: " + e.getMessage());
                e.printStackTrace();
                player.sendSystemMessage(Component.literal("§c[Kyokuerabu] 传输音乐文件时出错: " + e.getMessage()));
            }
        });
    }
    
    private static void sendMusicInChunks(ServerPlayer player, String fileName, byte[] musicData) {
        final int CHUNK_SIZE = 8 * 1024; // 8KB分块
        int totalChunks = (int) Math.ceil((double) musicData.length / CHUNK_SIZE);
        
        System.out.println("[Kyokuerabu] Sending music file " + fileName + " in " + totalChunks + " chunks to " + player.getName().getString());
        
        try {
            for (int i = 0; i < totalChunks; i++) {
                int start = i * CHUNK_SIZE;
                int end = Math.min(start + CHUNK_SIZE, musicData.length);
                int chunkLength = end - start;
                
                byte[] chunkData = new byte[chunkLength];
                System.arraycopy(musicData, start, chunkData, 0, chunkLength);
                
                PlayLocalMusicChunkPacket packet = new PlayLocalMusicChunkPacket(
                    fileName, chunkData, i, totalChunks, musicData.length
                );
                
                NetworkHandler.INSTANCE.send(
                    PacketDistributor.PLAYER.with(() -> player),
                    packet
                );
                
                System.out.println("[Kyokuerabu] Sent chunk " + (i + 1) + "/" + totalChunks + " to " + player.getName().getString());
                
                // 在分块之间添加延迟，避免网络拥塞
                if (i < totalChunks - 1) {
                    Thread.sleep(100);
                }
            }
            
            System.out.println("[Kyokuerabu] Finished sending all chunks for " + fileName + " to " + player.getName().getString());
            
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error sending music chunks: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static String calculateFileHash(byte[] data) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = md.digest(data);
        
        StringBuilder sb = new StringBuilder();
        for (byte b : hashBytes) {
            sb.append(String.format("%02x", b));
        }
        
        return sb.toString();
    }
    
    // 内部类：待处理的音乐请求
    private static class PendingMusicRequest {
        final String fileName;
        final byte[] musicData;
        final Collection<ServerPlayer> targets;
        final String fileHash;
        final long fileSize;
        
        PendingMusicRequest(String fileName, byte[] musicData, Collection<ServerPlayer> targets, String fileHash, long fileSize) {
            this.fileName = fileName;
            this.musicData = musicData;
            this.targets = targets;
            this.fileHash = fileHash;
            this.fileSize = fileSize;
        }
    }
}
