package top.lacrus.kyokuerabu.util;

import com.mojang.logging.LogUtils;
import org.slf4j.Logger;

import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class LocalMusicManager {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static final Path MUSIC_DIRECTORY = Paths.get("music");
    
    /**
     * 获取本地音乐目录中的所有MP3文件
     */
    public static List<String> getAvailableMusicFiles() {
        List<String> musicFiles = new ArrayList<>();
        
        if (!Files.exists(MUSIC_DIRECTORY)) {
            LOGGER.warn("Music directory does not exist: {}", MUSIC_DIRECTORY.toAbsolutePath());
            return musicFiles;
        }
        
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(MUSIC_DIRECTORY, "*.mp3")) {
            for (Path file : stream) {
                musicFiles.add(file.getFileName().toString());
            }
        } catch (IOException e) {
            LOGGER.error("Failed to list music files in directory: {}", MUSIC_DIRECTORY.toAbsolutePath(), e);
        }
        
        return musicFiles;
    }
    
    /**
     * 检查指定的音乐文件是否存在
     */
    public static boolean musicFileExists(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        // 确保文件名以.mp3结尾
        if (!fileName.toLowerCase().endsWith(".mp3")) {
            fileName += ".mp3";
        }
        
        Path musicFile = MUSIC_DIRECTORY.resolve(fileName);
        return Files.exists(musicFile) && Files.isRegularFile(musicFile);
    }
    
    /**
     * 获取音乐文件的完整路径
     */
    public static Path getMusicFilePath(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return null;
        }
        
        // 确保文件名以.mp3结尾
        if (!fileName.toLowerCase().endsWith(".mp3")) {
            fileName += ".mp3";
        }
        
        return MUSIC_DIRECTORY.resolve(fileName);
    }
    
    /**
     * 获取音乐目录的绝对路径
     */
    public static String getMusicDirectoryPath() {
        return MUSIC_DIRECTORY.toAbsolutePath().toString();
    }
    
    /**
     * 验证文件名是否安全（防止路径遍历攻击）
     */
    public static boolean isValidFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含路径分隔符或其他危险字符
        return !fileName.contains("..") && 
               !fileName.contains("/") && 
               !fileName.contains("\\") && 
               !fileName.contains(":") &&
               fileName.matches("[a-zA-Z0-9._\\-\\s\\u4e00-\\u9fff]+");
    }
}
