package top.lacrus.kyokuerabu.standalone;

import javazoom.jl.player.advanced.AdvancedPlayer;
import javazoom.jl.player.advanced.PlaybackEvent;
import javazoom.jl.player.advanced.PlaybackListener;

import javax.sound.sampled.*;
import java.io.*;
import java.lang.management.ManagementFactory;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 音乐下载器和播放器
 * 负责下载音乐文件并播放
 */
public class MusicDownloader {
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private static volatile boolean shouldExit = false;
    private static String minecraftProcessId;
    private static double volume = 1.0;

    public static void main(String[] args) {
        if (args.length < 5) {
            System.exit(1);
        }

        String downloadUrl = args[0];
        String fileName = args[1];
        String expectedHash = args[2];
        long expectedSize = Long.parseLong(args[3]);
        minecraftProcessId = args[4];
        
        if (args.length >= 6) {
            try {
                volume = Double.parseDouble(args[5]);
                volume = Math.max(0.0, Math.min(1.0, volume));
            } catch (NumberFormatException e) {
                volume = 1.0;
            }
        }

        try {
            // 启动Minecraft进程监控
            startMinecraftProcessMonitor();

            // 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                cleanup();
            }));

            // 下载并播放音乐
            downloadAndPlay(downloadUrl, fileName, expectedHash, expectedSize);

        } catch (Exception e) {
            System.exit(1);
        }
    }

    private static void downloadAndPlay(String downloadUrl, String fileName, String expectedHash, long expectedSize) {
        try {
            // 确保client_music目录存在
            Path musicDir = Paths.get("client_music");
            if (!Files.exists(musicDir)) {
                Files.createDirectories(musicDir);
            }

            Path musicFile = musicDir.resolve(fileName);

            // 下载文件
            if (downloadFile(downloadUrl, musicFile, expectedSize)) {
                // 验证文件哈希
                if (verifyFileHash(musicFile, expectedHash)) {
                    // 播放音乐
                    playAudioFile(musicFile);
                } else {
                    // 哈希验证失败，删除文件
                    Files.deleteIfExists(musicFile);
                    System.exit(1);
                }
            } else {
                System.exit(1);
            }

        } catch (Exception e) {
            System.exit(1);
        }
    }

    private static boolean downloadFile(String urlString, Path outputPath, long expectedSize) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时

            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                connection.disconnect();
                return false;
            }

            long contentLength = connection.getContentLengthLong();
            if (contentLength > 0 && contentLength != expectedSize) {
                connection.disconnect();
                return false;
            }

            // 简单的单线程下载
            try (InputStream in = connection.getInputStream();
                 OutputStream out = Files.newOutputStream(outputPath)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytesRead = 0;

                while ((bytesRead = in.read(buffer)) != -1 && !shouldExit) {
                    out.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;
                }

                connection.disconnect();

                // 检查下载是否完整
                return !shouldExit && (expectedSize <= 0 || totalBytesRead == expectedSize);

            }

        } catch (Exception e) {
            return false;
        }
    }

    private static boolean verifyFileHash(Path filePath, String expectedHash) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] fileBytes = Files.readAllBytes(filePath);
            byte[] hashBytes = digest.digest(fileBytes);

            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            String actualHash = sb.toString();
            return actualHash.equals(expectedHash);

        } catch (Exception e) {
            return false;
        }
    }

    private static void playAudioFile(Path audioFile) {
        try {
            // 如果音量为0，直接退出
            if (volume <= 0.0) {
                System.exit(0);
                return;
            }

            // 尝试系统音量控制
            trySystemVolumeControl();

            // 播放音频
            playWithAdvancedPlayer(audioFile);

        } catch (Exception e) {
            System.exit(1);
        }
    }

    private static boolean trySystemVolumeControl() {
        try {
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            for (Mixer.Info mixerInfo : mixers) {
                Mixer mixer = AudioSystem.getMixer(mixerInfo);
                if (mixer.isLineSupported(Port.Info.SPEAKER)) {
                    Port port = (Port) mixer.getLine(Port.Info.SPEAKER);
                    port.open();

                    if (port.isControlSupported(FloatControl.Type.VOLUME)) {
                        FloatControl volumeControl = (FloatControl) port.getControl(FloatControl.Type.VOLUME);
                        float currentVolume = volumeControl.getValue();
                        float newVolume = (float) (currentVolume * volume);
                        volumeControl.setValue(Math.max(volumeControl.getMinimum(),
                                             Math.min(volumeControl.getMaximum(), newVolume)));
                        port.close();
                        return true;
                    }
                    port.close();
                }
            }
        } catch (Exception e) {
            // 系统音量控制失败
        }
        return false;
    }

    private static void playWithAdvancedPlayer(Path audioFile) {
        try {
            FileInputStream fis = new FileInputStream(audioFile.toFile());
            BufferedInputStream bis = new BufferedInputStream(fis);

            AdvancedPlayer player = new AdvancedPlayer(bis);

            player.setPlayBackListener(new PlaybackListener() {
                @Override
                public void playbackStarted(PlaybackEvent evt) {
                    // 静默启动
                }

                @Override
                public void playbackFinished(PlaybackEvent evt) {
                    cleanup();
                    System.exit(0);
                }
            });

            // 播放线程
            Thread playThread = new Thread(() -> {
                try {
                    player.play();
                } catch (Exception e) {
                    // 播放出错
                }
            });

            playThread.start();

            // 等待播放完成或收到退出信号
            while (playThread.isAlive() && !shouldExit) {
                Thread.sleep(100);
            }

            if (shouldExit) {
                player.close();
            }

        } catch (Exception e) {
            System.exit(1);
        }
    }

    private static void startMinecraftProcessMonitor() {
        if (minecraftProcessId == null) {
            return;
        }

        scheduler.scheduleAtFixedRate(() -> {
            if (shouldExit) {
                return;
            }

            if (!isProcessAlive(minecraftProcessId)) {
                shouldExit = true;
                cleanup();
                System.exit(0);
            }
        }, 2, 2, TimeUnit.SECONDS);
    }

    private static boolean isProcessAlive(String pid) {
        try {
            if (System.getProperty("os.name").toLowerCase().contains("win")) {
                ProcessBuilder pb = new ProcessBuilder("tasklist", "/fi", "PID eq " + pid, "/fo", "csv");
                Process process = pb.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                int lineCount = 0;
                while ((line = reader.readLine()) != null) {
                    lineCount++;
                    if (lineCount > 1) {
                        return true;
                    }
                }
                return false;
            } else {
                ProcessBuilder pb = new ProcessBuilder("ps", "-p", pid);
                Process process = pb.start();
                return process.waitFor() == 0;
            }
        } catch (Exception e) {
            return false;
        }
    }

    private static void cleanup() {
        shouldExit = true;
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }
}
