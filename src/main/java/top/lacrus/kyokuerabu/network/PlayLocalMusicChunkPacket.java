package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

public class PlayLocalMusicChunkPacket {
    private final String fileName;
    private final byte[] chunkData;
    private final int chunkIndex;
    private final int totalChunks;
    private final int totalSize;
    
    public PlayLocalMusicChunkPacket(String fileName, byte[] chunkData, int chunkIndex, int totalChunks, int totalSize) {
        this.fileName = fileName;
        this.chunkData = chunkData;
        this.chunkIndex = chunkIndex;
        this.totalChunks = totalChunks;
        this.totalSize = totalSize;
    }
    
    public static void encode(PlayLocalMusicChunkPacket packet, FriendlyByteBuf buffer) {
        try {
            // 使用兼容的字符串写入方法
            buffer.writeUtf(packet.fileName, 32767);
            buffer.writeInt(packet.chunkIndex);
            buffer.writeInt(packet.totalChunks);
            buffer.writeInt(packet.totalSize);
            buffer.writeInt(packet.chunkData.length);
            buffer.writeBytes(packet.chunkData);
            System.out.println("[Kyokuerabu] Encoded chunk packet: " + packet.fileName + " chunk " + packet.chunkIndex);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error encoding chunk packet: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static PlayLocalMusicChunkPacket decode(FriendlyByteBuf buffer) {
        try {
            // 使用兼容的字符串读取方法
            String fileName = buffer.readUtf(32767);
            int chunkIndex = buffer.readInt();
            int totalChunks = buffer.readInt();
            int totalSize = buffer.readInt();
            int chunkLength = buffer.readInt();
            byte[] chunkData = new byte[chunkLength];
            buffer.readBytes(chunkData);
            System.out.println("[Kyokuerabu] Decoded chunk packet: " + fileName + " chunk " + chunkIndex);
            return new PlayLocalMusicChunkPacket(fileName, chunkData, chunkIndex, totalChunks, totalSize);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error decoding chunk packet: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    public static void handle(PlayLocalMusicChunkPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            System.out.println("[Kyokuerabu] Received PlayLocalMusicChunkPacket on side: " + context.getDirection());
            System.out.println("[Kyokuerabu] Music file: " + packet.fileName);
            System.out.println("[Kyokuerabu] Chunk: " + packet.chunkIndex + "/" + packet.totalChunks);
            System.out.println("[Kyokuerabu] Chunk size: " + packet.chunkData.length + " bytes");

            // 使用DistExecutor确保只在客户端执行
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> {
                System.out.println("[Kyokuerabu] DistExecutor: Running on client side for music chunk");
                handleClientSide(packet.fileName, packet.chunkData, packet.chunkIndex, packet.totalChunks, packet.totalSize);
            });
        });
        context.setPacketHandled(true);
    }

    // 这个方法只会在客户端被调用
    private static void handleClientSide(String fileName, byte[] chunkData, int chunkIndex, int totalChunks, int totalSize) {
        System.out.println("[Kyokuerabu] handleClientSide called for music chunk: " + fileName);
        try {
            // 调用客户端分块音乐接收器
            top.lacrus.kyokuerabu.client.ChunkedMusicReceiver.receiveChunk(fileName, chunkData, chunkIndex, totalChunks, totalSize);
            System.out.println("[Kyokuerabu] Successfully called ChunkedMusicReceiver.receiveChunk");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error calling ChunkedMusicReceiver.receiveChunk: " + e.getClass().getSimpleName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public byte[] getChunkData() {
        return chunkData;
    }
    
    public int getChunkIndex() {
        return chunkIndex;
    }
    
    public int getTotalChunks() {
        return totalChunks;
    }
    
    public int getTotalSize() {
        return totalSize;
    }
}
