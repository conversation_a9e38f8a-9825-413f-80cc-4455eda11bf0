package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

/**
 * 音乐分段请求包
 * 客户端向服务器请求特定的音乐文件分段
 */
public class MusicSegmentRequestPacket {
    private final String fileName;
    private final int segmentIndex;
    private final int segmentSize;
    private final long startByte;
    private final long endByte;

    public MusicSegmentRequestPacket(String fileName, int segmentIndex, int segmentSize, long startByte, long endByte) {
        this.fileName = fileName;
        this.segmentIndex = segmentIndex;
        this.segmentSize = segmentSize;
        this.startByte = startByte;
        this.endByte = endByte;
    }

    public static void encode(MusicSegmentRequestPacket packet, FriendlyByteBuf buffer) {
        buffer.writeUtf(packet.fileName);
        buffer.writeInt(packet.segmentIndex);
        buffer.writeInt(packet.segmentSize);
        buffer.writeLong(packet.startByte);
        buffer.writeLong(packet.endByte);
    }

    public static MusicSegmentRequestPacket decode(FriendlyByteBuf buffer) {
        String fileName = buffer.readUtf();
        int segmentIndex = buffer.readInt();
        int segmentSize = buffer.readInt();
        long startByte = buffer.readLong();
        long endByte = buffer.readLong();
        return new MusicSegmentRequestPacket(fileName, segmentIndex, segmentSize, startByte, endByte);
    }

    public static void handle(MusicSegmentRequestPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            // 在服务器端处理分段请求
            handleServerSide(packet, context);
        });
        context.setPacketHandled(true);
    }

    private static void handleServerSide(MusicSegmentRequestPacket packet, NetworkEvent.Context context) {
        try {
            System.out.println("[Kyokuerabu] Received segment request: " + packet.fileName + 
                             " segment " + packet.segmentIndex + " (" + packet.startByte + "-" + packet.endByte + ")");
            
            // 调用服务器端分段处理器
            top.lacrus.kyokuerabu.server.MusicSegmentHandler.handleSegmentRequest(
                packet.fileName, 
                packet.segmentIndex, 
                packet.segmentSize,
                packet.startByte, 
                packet.endByte, 
                context.getSender()
            );
            
        } catch (Exception e) {
            System.err.println("[Kyokuerabu] Error handling segment request: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Getters
    public String getFileName() { return fileName; }
    public int getSegmentIndex() { return segmentIndex; }
    public int getSegmentSize() { return segmentSize; }
    public long getStartByte() { return startByte; }
    public long getEndByte() { return endByte; }
}
