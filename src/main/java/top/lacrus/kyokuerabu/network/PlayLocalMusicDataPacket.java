package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

public class PlayLocalMusicDataPacket {
    private final String fileName;
    private final byte[] musicData;
    
    public PlayLocalMusicDataPacket(String fileName, byte[] musicData) {
        this.fileName = fileName;
        this.musicData = musicData;
    }
    
    public static void encode(PlayLocalMusicDataPacket packet, FriendlyByteBuf buffer) {
        try {
            buffer.writeUtf(packet.fileName, 32767);
            buffer.writeInt(packet.musicData.length);
            buffer.writeBytes(packet.musicData);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error encoding music data packet: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static PlayLocalMusicDataPacket decode(FriendlyByteBuf buffer) {
        try {
            String fileName = buffer.readUtf(32767);
            int dataLength = buffer.readInt();
            byte[] musicData = new byte[dataLength];
            buffer.readBytes(musicData);
            return new PlayLocalMusicDataPacket(fileName, musicData);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error decoding music data packet: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    public static void handle(PlayLocalMusicDataPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            System.out.println("[Kyokuerabu] Received PlayLocalMusicDataPacket on side: " + context.getDirection());
            System.out.println("[Kyokuerabu] Music file: " + packet.fileName);
            System.out.println("[Kyokuerabu] Music data size: " + packet.musicData.length + " bytes");

            // 使用DistExecutor确保只在客户端执行
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> {
                System.out.println("[Kyokuerabu] DistExecutor: Running on client side for local music data");
                handleClientSide(packet.fileName, packet.musicData);
            });
        });
        context.setPacketHandled(true);
    }

    // 这个方法只会在客户端被调用
    private static void handleClientSide(String fileName, byte[] musicData) {
        System.out.println("[Kyokuerabu] handleClientSide called for local music data: " + fileName);
        try {
            // 调用客户端MinecraftJLayerPlayer音乐播放器
            top.lacrus.kyokuerabu.client.MinecraftJLayerPlayer.playMusicFromMemory(fileName, musicData);
            System.out.println("[Kyokuerabu] Successfully called MinecraftJLayerPlayer.playMusicFromMemory");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error calling MinecraftJLayerPlayer.playMusicFromMemory: " + e.getClass().getSimpleName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public byte[] getMusicData() {
        return musicData;
    }
}
