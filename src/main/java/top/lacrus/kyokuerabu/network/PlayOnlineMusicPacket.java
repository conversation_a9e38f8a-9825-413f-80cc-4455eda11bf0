package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

/**
 * 在线音乐播放数据包
 * 服务器向客户端发送音乐URL，客户端下载并播放
 */
public class PlayOnlineMusicPacket {
    private final String musicUrl;
    private final String fileName; // 从URL提取的文件名
    
    public PlayOnlineMusicPacket(String musicUrl, String fileName) {
        this.musicUrl = musicUrl;
        this.fileName = fileName;
    }
    
    public static void encode(PlayOnlineMusicPacket packet, FriendlyByteBuf buffer) {
        try {
            buffer.writeUtf(packet.musicUrl, 32767);
            buffer.writeUtf(packet.fileName, 32767);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error encoding online music packet: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static PlayOnlineMusicPacket decode(FriendlyByteBuf buffer) {
        try {
            String musicUrl = buffer.readUtf(32767);
            String fileName = buffer.readUtf(32767);
            return new PlayOnlineMusicPacket(musicUrl, fileName);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error decoding online music packet: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    public static void handle(PlayOnlineMusicPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            System.out.println("[Kyokuerabu] Received PlayOnlineMusicPacket on side: " + context.getDirection());
            System.out.println("[Kyokuerabu] Music URL: " + packet.musicUrl);
            System.out.println("[Kyokuerabu] File name: " + packet.fileName);

            // 使用DistExecutor确保只在客户端执行
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> {
                System.out.println("[Kyokuerabu] DistExecutor: Running on client side for online music");
                handleClientSide(packet.musicUrl, packet.fileName);
            });
        });
        context.setPacketHandled(true);
    }

    // 这个方法只会在客户端被调用
    private static void handleClientSide(String musicUrl, String fileName) {
        System.out.println("[Kyokuerabu] handleClientSide called for online music: " + fileName);
        try {
            // 检查是否接受OP音乐推送
            if (!top.lacrus.kyokuerabu.client.MusicConfig.acceptOpMusic()) {
                System.out.println("[Kyokuerabu] Online music rejected by client settings");
                return;
            }
            
            // 调用客户端在线音乐下载器
            top.lacrus.kyokuerabu.client.OnlineMusicDownloader.downloadAndPlay(musicUrl, fileName);
            System.out.println("[Kyokuerabu] Successfully called OnlineMusicDownloader.downloadAndPlay");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error calling OnlineMusicDownloader.downloadAndPlay: " + e.getClass().getSimpleName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public String getMusicUrl() {
        return musicUrl;
    }
    
    public String getFileName() {
        return fileName;
    }
}
