package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

/**
 * 简化的音乐包，使用更兼容的序列化方式
 */
public class SimpleMusicPacket {
    private final String fileName;
    private final byte[] musicData;
    
    public SimpleMusicPacket(String fileName, byte[] musicData) {
        this.fileName = fileName;
        this.musicData = musicData;
    }
    
    public static void encode(SimpleMusicPacket packet, FriendlyByteBuf buffer) {
        try {
            // 使用字节数组方式写入字符串，避免版本兼容问题
            byte[] fileNameBytes = packet.fileName.getBytes("UTF-8");
            buffer.writeInt(fileNameBytes.length);
            buffer.writeBytes(fileNameBytes);
            
            // 写入音乐数据
            buffer.writeInt(packet.musicData.length);
            buffer.writeBytes(packet.musicData);
            
            System.out.println("[Kyokuerabu] Encoded SimpleMusicPacket: " + packet.fileName + " (" + packet.musicData.length + " bytes)");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error encoding SimpleMusicPacket: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static SimpleMusicPacket decode(FriendlyByteBuf buffer) {
        try {
            // 读取文件名
            int fileNameLength = buffer.readInt();
            byte[] fileNameBytes = new byte[fileNameLength];
            buffer.readBytes(fileNameBytes);
            String fileName = new String(fileNameBytes, "UTF-8");
            
            // 读取音乐数据
            int dataLength = buffer.readInt();
            byte[] musicData = new byte[dataLength];
            buffer.readBytes(musicData);
            
            System.out.println("[Kyokuerabu] Decoded SimpleMusicPacket: " + fileName + " (" + musicData.length + " bytes)");
            return new SimpleMusicPacket(fileName, musicData);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error decoding SimpleMusicPacket: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to decode SimpleMusicPacket", e);
        }
    }
    
    public static void handle(SimpleMusicPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            System.out.println("[Kyokuerabu] Received SimpleMusicPacket on side: " + context.getDirection());
            System.out.println("[Kyokuerabu] Music file: " + packet.fileName);
            System.out.println("[Kyokuerabu] Music data size: " + packet.musicData.length + " bytes");

            // 使用DistExecutor确保只在客户端执行
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> {
                System.out.println("[Kyokuerabu] DistExecutor: Running on client side for simple music");
                handleClientSide(packet.fileName, packet.musicData);
            });
        });
        context.setPacketHandled(true);
    }

    // 这个方法只会在客户端被调用
    private static void handleClientSide(String fileName, byte[] musicData) {
        System.out.println("[Kyokuerabu] handleClientSide called for simple music: " + fileName);
        try {
            // 调用MinecraftJLayerPlayer播放器
            top.lacrus.kyokuerabu.client.MinecraftJLayerPlayer.playMusicFromMemory(fileName, musicData);
            System.out.println("[Kyokuerabu] Successfully called MinecraftJLayerPlayer.playMusicFromMemory");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error calling MinecraftJLayerPlayer.playMusicFromMemory: " + e.getClass().getSimpleName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public byte[] getMusicData() {
        return musicData;
    }
}
