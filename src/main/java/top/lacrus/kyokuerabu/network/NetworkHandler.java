package top.lacrus.kyokuerabu.network;

import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.simple.SimpleChannel;
import top.lacrus.kyokuerabu.Kyokuerabu;

public class NetworkHandler {
    private static final String PROTOCOL_VERSION = "1";
    
    public static final SimpleChannel INSTANCE = NetworkRegistry.newSimpleChannel(
        new ResourceLocation(Kyokuerabu.MODID, "main"),
        () -> PROTOCOL_VERSION,
        PROTOCOL_VERSION::equals,
        PROTOCOL_VERSION::equals
    );
    
    private static int packetId = 0;
    
    public static void registerPackets() {
        System.out.println("[Kyokuerabu] Registering network packets...");
        
        // 注册在线音乐播放包
        INSTANCE.registerMessage(
            packetId++,
            PlayMusicPacket.class,
            PlayMusicPacket::encode,
            PlayMusicPacket::decode,
            PlayMusicPacket::handle
        );
        System.out.println("[<PERSON><PERSON><PERSON><PERSON><PERSON>] PlayMusicPacket registered with ID: " + (packetId - 1));
        
        // 注册本地音乐播放包
        INSTANCE.registerMessage(
            packetId++,
            PlayLocalMusicPacket.class,
            PlayLocalMusicPacket::encode,
            PlayLocalMusicPacket::decode,
            PlayLocalMusicPacket::handle
        );
        System.out.println("[Kyokuerabu] PlayLocalMusicPacket registered with ID: " + (packetId - 1));

        // 注册音乐分段请求包
        INSTANCE.registerMessage(
            packetId++,
            MusicSegmentRequestPacket.class,
            MusicSegmentRequestPacket::encode,
            MusicSegmentRequestPacket::decode,
            MusicSegmentRequestPacket::handle
        );
        System.out.println("[Kyokuerabu] MusicSegmentRequestPacket registered with ID: " + (packetId - 1));

        // 注册音乐分段响应包
        INSTANCE.registerMessage(
            packetId++,
            MusicSegmentResponsePacket.class,
            MusicSegmentResponsePacket::encode,
            MusicSegmentResponsePacket::decode,
            MusicSegmentResponsePacket::handle
        );
        System.out.println("[Kyokuerabu] MusicSegmentResponsePacket registered with ID: " + (packetId - 1));

        // 注册在线音乐播放包
        INSTANCE.registerMessage(
            packetId++,
            PlayOnlineMusicPacket.class,
            PlayOnlineMusicPacket::encode,
            PlayOnlineMusicPacket::decode,
            PlayOnlineMusicPacket::handle
        );
        System.out.println("[Kyokuerabu] PlayOnlineMusicPacket registered with ID: " + (packetId - 1));

        // 注册简化音乐包（用于小文件）
        INSTANCE.registerMessage(
            packetId++,
            SimpleMusicPacket.class,
            SimpleMusicPacket::encode,
            SimpleMusicPacket::decode,
            SimpleMusicPacket::handle
        );
        System.out.println("[Kyokuerabu] SimpleMusicPacket registered with ID: " + (packetId - 1));

        // 注册音乐文件信息包
        INSTANCE.registerMessage(
            packetId++,
            MusicFileInfoPacket.class,
            MusicFileInfoPacket::encode,
            MusicFileInfoPacket::decode,
            MusicFileInfoPacket::handle
        );
        System.out.println("[Kyokuerabu] MusicFileInfoPacket registered with ID: " + (packetId - 1));

        // 注册音乐缓存响应包
        INSTANCE.registerMessage(
            packetId++,
            MusicCacheResponsePacket.class,
            MusicCacheResponsePacket::encode,
            MusicCacheResponsePacket::decode,
            MusicCacheResponsePacket::handle
        );
        System.out.println("[Kyokuerabu] MusicCacheResponsePacket registered with ID: " + (packetId - 1));
    }
}
