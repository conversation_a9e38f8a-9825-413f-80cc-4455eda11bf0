package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

/**
 * 客户端响应包，告知服务器是否需要重新下载文件
 */
public class MusicCacheResponsePacket {
    private final String fileName;
    private final boolean needDownload;
    private final String reason;
    
    public MusicCacheResponsePacket(String fileName, boolean needDownload, String reason) {
        this.fileName = fileName;
        this.needDownload = needDownload;
        this.reason = reason;
    }
    
    public static void encode(MusicCacheResponsePacket packet, FriendlyByteBuf buffer) {
        try {
            // 使用字节数组方式写入字符串
            byte[] fileNameBytes = packet.fileName.getBytes("UTF-8");
            buffer.writeInt(fileNameBytes.length);
            buffer.writeBytes(fileNameBytes);
            
            buffer.writeBoolean(packet.needDownload);
            
            byte[] reasonBytes = packet.reason.getBytes("UTF-8");
            buffer.writeInt(reasonBytes.length);
            buffer.writeBytes(reasonBytes);
            
            System.out.println("[Kyo<PERSON>erabu] Encoded MusicCacheResponsePacket: " + packet.fileName + " (needDownload: " + packet.needDownload + ", reason: " + packet.reason + ")");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error encoding MusicCacheResponsePacket: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static MusicCacheResponsePacket decode(FriendlyByteBuf buffer) {
        try {
            // 读取文件名
            int fileNameLength = buffer.readInt();
            byte[] fileNameBytes = new byte[fileNameLength];
            buffer.readBytes(fileNameBytes);
            String fileName = new String(fileNameBytes, "UTF-8");
            
            // 读取是否需要下载
            boolean needDownload = buffer.readBoolean();
            
            // 读取原因
            int reasonLength = buffer.readInt();
            byte[] reasonBytes = new byte[reasonLength];
            buffer.readBytes(reasonBytes);
            String reason = new String(reasonBytes, "UTF-8");
            
            System.out.println("[Kyokuerabu] Decoded MusicCacheResponsePacket: " + fileName + " (needDownload: " + needDownload + ", reason: " + reason + ")");
            return new MusicCacheResponsePacket(fileName, needDownload, reason);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error decoding MusicCacheResponsePacket: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to decode MusicCacheResponsePacket", e);
        }
    }
    
    public static void handle(MusicCacheResponsePacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            System.out.println("[Kyokuerabu] Received MusicCacheResponsePacket on server side");
            System.out.println("[Kyokuerabu] Music file: " + packet.fileName);
            System.out.println("[Kyokuerabu] Need download: " + packet.needDownload);
            System.out.println("[Kyokuerabu] Reason: " + packet.reason);

            // 调用服务器端处理
            handleServerSide(packet.fileName, packet.needDownload, packet.reason, context);
        });
        context.setPacketHandled(true);
    }

    // 这个方法在服务器端被调用
    private static void handleServerSide(String fileName, boolean needDownload, String reason, NetworkEvent.Context context) {
        System.out.println("[Kyokuerabu] handleServerSide called for cache response: " + fileName);
        try {
            // 调用服务器端缓存管理器
            top.lacrus.kyokuerabu.server.ServerMusicCacheManager.handleCacheResponse(
                context.getSender(), fileName, needDownload, reason
            );
            System.out.println("[Kyokuerabu] Successfully called ServerMusicCacheManager.handleCacheResponse");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error calling ServerMusicCacheManager.handleCacheResponse: " + e.getClass().getSimpleName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public boolean isNeedDownload() {
        return needDownload;
    }
    
    public String getReason() {
        return reason;
    }
}
