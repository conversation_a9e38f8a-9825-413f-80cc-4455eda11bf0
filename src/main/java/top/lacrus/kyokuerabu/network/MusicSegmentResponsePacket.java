package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

/**
 * 音乐分段响应包
 * 服务器向客户端发送音乐文件分段数据
 */
public class MusicSegmentResponsePacket {
    private final String fileName;
    private final int segmentIndex;
    private final int totalSegments;
    private final long startByte;
    private final long endByte;
    private final byte[] segmentData;
    private final boolean isLastSegment;

    public MusicSegmentResponsePacket(String fileName, int segmentIndex, int totalSegments, 
                                    long startByte, long endByte, byte[] segmentData, boolean isLastSegment) {
        this.fileName = fileName;
        this.segmentIndex = segmentIndex;
        this.totalSegments = totalSegments;
        this.startByte = startByte;
        this.endByte = endByte;
        this.segmentData = segmentData;
        this.isLastSegment = isLastSegment;
    }

    public static void encode(MusicSegmentResponsePacket packet, FriendlyByteBuf buffer) {
        buffer.writeUtf(packet.fileName);
        buffer.writeInt(packet.segmentIndex);
        buffer.writeInt(packet.totalSegments);
        buffer.writeLong(packet.startByte);
        buffer.writeLong(packet.endByte);
        buffer.writeBoolean(packet.isLastSegment);
        
        // 写入分段数据
        buffer.writeInt(packet.segmentData.length);
        buffer.writeBytes(packet.segmentData);
    }

    public static MusicSegmentResponsePacket decode(FriendlyByteBuf buffer) {
        String fileName = buffer.readUtf();
        int segmentIndex = buffer.readInt();
        int totalSegments = buffer.readInt();
        long startByte = buffer.readLong();
        long endByte = buffer.readLong();
        boolean isLastSegment = buffer.readBoolean();
        
        // 读取分段数据
        int dataLength = buffer.readInt();
        byte[] segmentData = new byte[dataLength];
        buffer.readBytes(segmentData);
        
        return new MusicSegmentResponsePacket(fileName, segmentIndex, totalSegments, 
                                            startByte, endByte, segmentData, isLastSegment);
    }

    public static void handle(MusicSegmentResponsePacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            // 在客户端处理分段响应
            handleClientSide(packet);
        });
        context.setPacketHandled(true);
    }

    private static void handleClientSide(MusicSegmentResponsePacket packet) {
        try {
            System.out.println("[Kyokuerabu] Received segment response: " + packet.fileName + 
                             " segment " + packet.segmentIndex + "/" + packet.totalSegments + 
                             " (" + packet.segmentData.length + " bytes)");
            
            // 调用客户端分段接收器
            top.lacrus.kyokuerabu.client.MusicSegmentReceiver.handleSegmentResponse(packet);
            
        } catch (Exception e) {
            System.err.println("[Kyokuerabu] Error handling segment response: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Getters
    public String getFileName() { return fileName; }
    public int getSegmentIndex() { return segmentIndex; }
    public int getTotalSegments() { return totalSegments; }
    public long getStartByte() { return startByte; }
    public long getEndByte() { return endByte; }
    public byte[] getSegmentData() { return segmentData; }
    public boolean isLastSegment() { return isLastSegment; }
}
