package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

/**
 * 音乐文件信息包，包含文件名、哈希值和文件大小
 * 用于客户端验证是否需要重新下载文件
 */
public class MusicFileInfoPacket {
    private final String fileName;
    private final String fileHash;
    private final long fileSize;
    
    public MusicFileInfoPacket(String fileName, String fileHash, long fileSize) {
        this.fileName = fileName;
        this.fileHash = fileHash;
        this.fileSize = fileSize;
    }
    
    public static void encode(MusicFileInfoPacket packet, FriendlyByteBuf buffer) {
        try {
            // 使用字节数组方式写入字符串，避免版本兼容问题
            byte[] fileNameBytes = packet.fileName.getBytes("UTF-8");
            buffer.writeInt(fileNameBytes.length);
            buffer.writeBytes(fileNameBytes);
            
            byte[] hashBytes = packet.fileHash.getBytes("UTF-8");
            buffer.writeInt(hashBytes.length);
            buffer.writeBytes(hashBytes);
            
            buffer.writeLong(packet.fileSize);
            
            System.out.println("[Kyokuerabu] Encoded MusicFileInfoPacket: " + packet.fileName + " (hash: " + packet.fileHash + ", size: " + packet.fileSize + ")");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error encoding MusicFileInfoPacket: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static MusicFileInfoPacket decode(FriendlyByteBuf buffer) {
        try {
            // 读取文件名
            int fileNameLength = buffer.readInt();
            byte[] fileNameBytes = new byte[fileNameLength];
            buffer.readBytes(fileNameBytes);
            String fileName = new String(fileNameBytes, "UTF-8");
            
            // 读取哈希
            int hashLength = buffer.readInt();
            byte[] hashBytes = new byte[hashLength];
            buffer.readBytes(hashBytes);
            String fileHash = new String(hashBytes, "UTF-8");
            
            // 读取文件大小
            long fileSize = buffer.readLong();
            
            System.out.println("[Kyokuerabu] Decoded MusicFileInfoPacket: " + fileName + " (hash: " + fileHash + ", size: " + fileSize + ")");
            return new MusicFileInfoPacket(fileName, fileHash, fileSize);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error decoding MusicFileInfoPacket: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to decode MusicFileInfoPacket", e);
        }
    }
    
    public static void handle(MusicFileInfoPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            System.out.println("[Kyokuerabu] Received MusicFileInfoPacket on side: " + context.getDirection());
            System.out.println("[Kyokuerabu] Music file: " + packet.fileName);
            System.out.println("[Kyokuerabu] File hash: " + packet.fileHash);
            System.out.println("[Kyokuerabu] File size: " + packet.fileSize);

            // 使用DistExecutor确保只在客户端执行
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> {
                System.out.println("[Kyokuerabu] DistExecutor: Running on client side for music file info");
                handleClientSide(packet.fileName, packet.fileHash, packet.fileSize);
            });
        });
        context.setPacketHandled(true);
    }

    // 这个方法只会在客户端被调用
    private static void handleClientSide(String fileName, String fileHash, long fileSize) {
        System.out.println("[Kyokuerabu] handleClientSide called for music file info: " + fileName);
        try {
            // 检查是否接受OP音乐推送
            if (!top.lacrus.kyokuerabu.client.MusicConfig.acceptOpMusic()) {
                System.out.println("[Kyokuerabu] OP music rejected by client settings");
                // 静默拒绝，不显示提示
                return;
            }

            // 调用客户端文件缓存管理器
            top.lacrus.kyokuerabu.client.MusicCacheManager.handleFileInfo(fileName, fileHash, fileSize);
            System.out.println("[Kyokuerabu] Successfully called MusicCacheManager.handleFileInfo");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error calling MusicCacheManager.handleFileInfo: " + e.getClass().getSimpleName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public String getFileHash() {
        return fileHash;
    }
    
    public long getFileSize() {
        return fileSize;
    }
}
