package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;
import top.lacrus.kyokuerabu.client.ClientMusicPlayer;

import java.util.function.Supplier;

public class PlayMusicPacket {
    private final String musicUrl;
    
    public PlayMusicPacket(String musicUrl) {
        this.musicUrl = musicUrl;
    }
    
    public static void encode(PlayMusicPacket packet, FriendlyByteBuf buffer) {
        try {
            buffer.writeUtf(packet.musicUrl, 32767);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error encoding music packet: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static PlayMusicPacket decode(FriendlyByteBuf buffer) {
        try {
            return new PlayMusicPacket(buffer.readUtf(32767));
        } catch (Exception e) {
            System.out.println("[Kyo<PERSON>erabu] Error decoding music packet: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    public static void handle(PlayMusicPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            System.out.println("[Kyokuerabu] Received PlayMusicPacket on side: " + context.getDirection());
            System.out.println("[Kyokuerabu] Music URL: " + packet.musicUrl);

            // 使用DistExecutor确保只在客户端执行
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> {
                System.out.println("[Kyokuerabu] DistExecutor: Running on client side");
                handleClientSide(packet.musicUrl);
            });
        });
        context.setPacketHandled(true);
    }

    // 这个方法只会在客户端被调用
    private static void handleClientSide(String musicUrl) {
        System.out.println("[Kyokuerabu] handleClientSide called with URL: " + musicUrl);
        try {
            // 直接调用客户端方法
            top.lacrus.kyokuerabu.client.ClientMusicPlayer.playMusic(musicUrl);
            System.out.println("[Kyokuerabu] Successfully called ClientMusicPlayer.playMusic");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error calling ClientMusicPlayer.playMusic: " + e.getClass().getSimpleName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public String getMusicUrl() {
        return musicUrl;
    }
}
