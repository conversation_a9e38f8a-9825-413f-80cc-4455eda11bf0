package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

public class PlayLocalMusicPacket {
    private final String fileName;
    private final String serverMusicPath;
    
    public PlayLocalMusicPacket(String fileName, String serverMusicPath) {
        this.fileName = fileName;
        this.serverMusicPath = serverMusicPath;
    }
    
    public static void encode(PlayLocalMusicPacket packet, FriendlyByteBuf buffer) {
        try {
            buffer.writeUtf(packet.fileName, 32767);
            buffer.writeUtf(packet.serverMusicPath, 32767);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error encoding local music packet: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static PlayLocalMusicPacket decode(FriendlyByteBuf buffer) {
        try {
            return new PlayLocalMusicPacket(buffer.readUtf(32767), buffer.readUtf(32767));
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error decoding local music packet: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    public static void handle(PlayLocalMusicPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            System.out.println("[Kyokuerabu] Received PlayLocalMusicPacket on side: " + context.getDirection());
            System.out.println("[Kyokuerabu] Music file: " + packet.fileName);
            System.out.println("[Kyokuerabu] Server music path: " + packet.serverMusicPath);

            // 使用DistExecutor确保只在客户端执行
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> {
                System.out.println("[Kyokuerabu] DistExecutor: Running on client side for local music");
                handleClientSide(packet.fileName, packet.serverMusicPath);
            });
        });
        context.setPacketHandled(true);
    }

    // 这个方法只会在客户端被调用
    private static void handleClientSide(String fileName, String serverMusicPath) {
        System.out.println("[Kyokuerabu] handleClientSide called for local music: " + fileName);
        try {
            // 调用客户端本地音乐播放器
            top.lacrus.kyokuerabu.client.ClientLocalMusicPlayer.playLocalMusic(fileName, serverMusicPath);
            System.out.println("[Kyokuerabu] Successfully called ClientLocalMusicPlayer.playLocalMusic");
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error calling ClientLocalMusicPlayer.playLocalMusic: " + e.getClass().getSimpleName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public String getServerMusicPath() {
        return serverMusicPath;
    }
}
