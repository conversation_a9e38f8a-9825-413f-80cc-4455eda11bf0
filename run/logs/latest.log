[297月2025 19:02:46.740] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher running: args [--launchTarget, forgeclientuserdev, --version, MOD_DEV, --assetIndex, 5, --assetsDir, C:\Users\<USER>\.gradle\caches\forge_gradle\assets, --gameDir, ., --fml.forgeVersion, 47.4.4, --fml.mcVersion, 1.20.1, --fml.forgeGroup, net.minecraftforge, --fml.mcpVersion, ********.114412, --mixin.config, kyokuerabu.mixins.json]
[297月2025 19:02:46.758] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher 10.0.9+10.0.9+main.dcd20f30 starting: java version 17.0.12 by Oracle Corporation; OS Windows 11 arch amd64 version 10.0
[297月2025 19:02:46.959] [main/INFO] [net.minecraftforge.fml.loading.ImmediateWindowHandler/]: Loading ImmediateWindowProvider fmlearlywindow
[297月2025 19:02:47.060] [main/INFO] [EARLYDISPLAY/]: Trying GL version 4.6
[297月2025 19:02:47.271] [main/INFO] [EARLYDISPLAY/]: Requested GL version 4.6 got version 4.6
[297月2025 19:02:47.360] [main/INFO] [mixin/]: SpongePowered MIXIN Subsystem Version=0.8.5 Source=union:/C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.spongepowered/mixin/0.8.5/9d1c0c3a304ae6697ecd477218fa61b850bf57fc/mixin-0.8.5.jar%23128!/ Service=ModLauncher Env=CLIENT
[297月2025 19:02:47.462] [pool-2-thread-1/INFO] [EARLYDISPLAY/]: GL info: NVIDIA GeForce GTX 1080/PCIe/SSE2 GL version 4.6.0 NVIDIA 560.94, NVIDIA Corporation
[297月2025 19:02:47.906] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\javafmllanguage\1.20.1-47.4.4\c05c04093d241dea1d0f54782d19cfe5db9d0aac\javafmllanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:02:47.911] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\lowcodelanguage\1.20.1-47.4.4\8a8bd6d44f3f0e6a1145b9066eac84fdcfbe7108\lowcodelanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:02:47.916] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\mclanguage\1.20.1-47.4.4\a8876f0a1a04237fc48a4a73692ea08e0443d2ba\mclanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:02:47.920] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\fmlcore\1.20.1-47.4.4\55a693260a8a7b851c3531972ef997294c191b3e\fmlcore-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:02:48.046] [main/INFO] [net.minecraftforge.fml.loading.moddiscovery.JarInJarDependencyLocator/]: No dependencies to load found. Skipping!
[297月2025 19:02:50.307] [main/INFO] [cpw.mods.modlauncher.LaunchServiceHandler/MODLAUNCHER]: Launching target 'forgeclientuserdev' with arguments [--version, MOD_DEV, --gameDir, ., --assetsDir, C:\Users\<USER>\.gradle\caches\forge_gradle\assets, --assetIndex, 5]
[297月2025 19:02:50.593] [main/WARN] [mixin/]: Reference map 'kyokuerabu.refmap.json' for kyokuerabu.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 19:02:57.117] [Datafixer Bootstrap/INFO] [com.mojang.datafixers.DataFixerBuilder/]: 188 Datafixer optimizations took 300 milliseconds
[297月2025 19:02:59.915] [Render thread/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/C:/Users/<USER>/.gradle/caches/forge_gradle/minecraft_user_repo/net/minecraftforge/forge/1.20.1-47.4.4_mapped_official_1.20.1/forge-1.20.1-47.4.4_mapped_official_1.20.1.jar%23191!/assets/.mcassetsroot' uses unexpected schema
[297月2025 19:02:59.916] [Render thread/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/C:/Users/<USER>/.gradle/caches/forge_gradle/minecraft_user_repo/net/minecraftforge/forge/1.20.1-47.4.4_mapped_official_1.20.1/forge-1.20.1-47.4.4_mapped_official_1.20.1.jar%23191!/data/.mcassetsroot' uses unexpected schema
[297月2025 19:02:59.968] [Render thread/INFO] [com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService/]: Environment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[297月2025 19:02:59.990] [Render thread/INFO] [net.minecraft.client.Minecraft/]: Setting user: Dev
[297月2025 19:03:00.113] [Render thread/INFO] [net.minecraft.client.Minecraft/]: Backend library: LWJGL version 3.3.1 build 7
[297月2025 19:03:00.551] [modloading-worker-0/INFO] [net.minecraftforge.common.ForgeMod/FORGEMOD]: Forge mod loading, version 47.4.4, for MC 1.20.1 with MCP ********.114412
[297月2025 19:03:00.551] [modloading-worker-0/INFO] [net.minecraftforge.common.MinecraftForge/FORGE]: MinecraftForge v47.4.4 Initialized
[297月2025 19:03:01.118] [Render thread/INFO] [net.minecraft.server.packs.repository.FolderRepositorySource/]: Found non-pack entry '.\resourcepacks\kyokuerabu_music', ignoring
[297月2025 19:03:01.631] [Render thread/INFO] [net.minecraftforge.gametest.ForgeGameTestHooks/]: Enabled Gametest Namespaces: [kyokuerabu]
[297月2025 19:03:01.781] [Render thread/INFO] [net.minecraft.server.packs.resources.ReloadableResourceManager/]: Reloading ResourceManager: vanilla, mod_resources
[297月2025 19:03:02.051] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [forge] Starting version check at https://files.minecraftforge.net/net/minecraftforge/forge/promotions_slim.json
[297月2025 19:03:02.074] [Worker-Main-6/INFO] [net.minecraft.client.gui.font.providers.UnihexProvider/]: Found unifont_all_no_pua-15.0.06.hex, loading
[297月2025 19:03:03.471] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [forge] Found status: AHEAD Current: 47.4.4 Target: null
[297月2025 19:03:05.546] [Render thread/WARN] [net.minecraft.client.sounds.SoundEngine/]: Missing sound for event: minecraft:item.goat_horn.play
[297月2025 19:03:05.546] [Render thread/WARN] [net.minecraft.client.sounds.SoundEngine/]: Missing sound for event: minecraft:entity.goat.screaming.horn_break
[297月2025 19:03:05.590] [Render thread/INFO] [com.mojang.blaze3d.audio.Library/]: OpenAL initialized on device OpenAL Soft on 扬声器 (EDIFIER N300)
[297月2025 19:03:05.591] [Render thread/INFO] [net.minecraft.client.sounds.SoundEngine/SOUNDS]: Sound engine started
[297月2025 19:03:05.727] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[297月2025 19:03:05.739] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[297月2025 19:03:05.741] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[297月2025 19:03:05.742] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[297月2025 19:03:05.742] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[297月2025 19:03:05.746] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[297月2025 19:03:05.748] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[297月2025 19:03:05.748] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[297月2025 19:03:05.749] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[297月2025 19:03:06.075] [Render thread/WARN] [net.minecraft.client.renderer.ShaderInstance/]: Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[297月2025 19:03:06.136] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x0 minecraft:textures/atlas/particles.png-atlas
[297月2025 19:03:06.138] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[297月2025 19:03:06.138] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 128x128x0 minecraft:textures/atlas/mob_effects.png-atlas
[297月2025 19:03:07.031] [Realms Notification Availability checker #1/INFO] [com.mojang.realmsclient.client.RealmsClient/]: Could not authorize you against Realms server: java.lang.RuntimeException: Failed to parse into SignedJWT: 0
[297月2025 19:03:23.760] [Render thread/INFO] [net.minecraftforge.registries.GameData/REGISTRIES]: Injecting existing registry data into this CLIENT instance
[297月2025 19:03:25.730] [Render thread/INFO] [net.minecraft.world.item.crafting.RecipeManager/]: Loaded 7 recipes
[297月2025 19:03:25.885] [Render thread/INFO] [net.minecraft.advancements.AdvancementList/]: Loaded 1271 advancements
[297月2025 19:03:26.369] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Starting integrated minecraft server version 1.20.1
[297月2025 19:03:26.369] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Generating keypair
[297月2025 19:03:27.353] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Preparing start region for dimension minecraft:overworld
[297月2025 19:03:30.217] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:03:30.217] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:03:30.217] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:03:30.218] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:03:30.218] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:03:30.218] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:03:30.380] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:03:30.884] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：44%
[297月2025 19:03:31.217] [Server thread/INFO] [net.minecraftforge.server.permission.PermissionAPI/]: Successfully initialized permission handler forge:default_handler
[297月2025 19:03:31.218] [Server thread/INFO] [top.lacrus.kyokuerabu.Kyokuerabu/]: Local music directory already exists: D:\Projects\Kyokuerabu\run\music
[297月2025 19:03:31.218] [Server thread/INFO] [top.lacrus.kyokuerabu.Kyokuerabu/]: Kyokuerabu server started - using packet-based music transfer
[297月2025 19:03:31.224] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: Time elapsed: 3859 ms
[297月2025 19:03:31.354] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Changing view distance to 12, from 10
[297月2025 19:03:31.356] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Changing simulation distance to 12, from 0
[297月2025 19:03:31.523] [Render thread/WARN] [io.netty.util.internal.SystemPropertyUtil/]: Unable to parse the boolean system property 'java.net.preferIPv6Addresses':system - using the default value: false
[297月2025 19:03:32.691] [Netty Local Client IO #0/INFO] [net.minecraftforge.network.NetworkHooks/]: Connected to a modded server.
[297月2025 19:03:32.757] [Server thread/INFO] [net.minecraft.server.players.PlayerList/]: Dev[local:E:772863c7] logged in with entity id 171 at (-76.54450054826061, 67.79673560066871, -142.18946886851253)
[297月2025 19:03:32.855] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Dev加入了游戏
[297月2025 19:03:33.770] [Render thread/INFO] [net.minecraft.advancements.AdvancementList/]: Loaded 2 advancements
[297月2025 19:03:55.411] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: [Dev: 已向玩家 Dev 发送在线音乐播放指令: qhc.mp3 (http://127.0.0.1/qhc.mp3)]
[297月2025 19:03:55.417] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.OnlineMusicDownloader/]: Starting online music download: qhc.mp3 from http://127.0.0.1/qhc.mp3
[297月2025 19:03:55.417] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.OnlineMusicDownloader/]: Music file already exists, playing directly: qhc.mp3
[297月2025 19:03:55.419] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] 已向玩家 Dev 发送在线音乐播放指令: qhc.mp3 (http://127.0.0.1/qhc.mp3)
[297月2025 19:03:55.420] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicDurationReader/]: Starting async duration calculation for: client_music\qhc.mp3
[297月2025 19:03:55.422] [ForkJoinPool.commonPool-worker-2/INFO] [top.lacrus.kyokuerabu.client.MusicDurationReader/]: Getting duration for file: qhc.mp3 (client_music\qhc.mp3)
[297月2025 19:03:55.423] [ForkJoinPool.commonPool-worker-2/ERROR] [top.lacrus.kyokuerabu.client.MusicDurationReader/]: Failed to get duration async: java.lang.NoClassDefFoundError: javazoom/jl/decoder/Bitstream
java.util.concurrent.CompletionException: java.lang.NoClassDefFoundError: javazoom/jl/decoder/Bitstream
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315) ~[?:?]
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320) ~[?:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1770) ~[?:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760) ~[?:?]
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373) ~[?:?]
	at java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182) ~[?:?]
	at java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655) ~[?:?]
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622) ~[?:?]
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165) ~[?:?]
Caused by: java.lang.NoClassDefFoundError: javazoom/jl/decoder/Bitstream
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getMp3Duration(MusicDurationReader.java:99) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getDurationMillis(MusicDurationReader.java:41) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.lambda$getDurationAsync$0(MusicDurationReader.java:249) ~[main/:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768) ~[?:?]
	... 6 more
Caused by: java.lang.ClassNotFoundException: javazoom.jl.decoder.Bitstream
	at jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[?:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[?:?]
	at cpw.mods.cl.ModuleClassLoader.loadClass(ModuleClassLoader.java:137) ~[securejarhandler-2.1.10.jar:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[?:?]
	at cpw.mods.cl.ModuleClassLoader.loadClass(ModuleClassLoader.java:137) ~[securejarhandler-2.1.10.jar:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[?:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getMp3Duration(MusicDurationReader.java:99) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getDurationMillis(MusicDurationReader.java:41) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.lambda$getDurationAsync$0(MusicDurationReader.java:249) ~[main/:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768) ~[?:?]
	... 6 more
[297月2025 19:03:55.437] [ForkJoinPool.commonPool-worker-2/INFO] [top.lacrus.kyokuerabu.client.MusicDurationReader/]: Starting async duration calculation for: client_music\qhc.mp3
[297月2025 19:03:55.438] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicDurationReader/]: Getting duration for file: qhc.mp3 (client_music\qhc.mp3)
[297月2025 19:03:55.438] [ForkJoinPool.commonPool-worker-1/ERROR] [top.lacrus.kyokuerabu.client.MusicDurationReader/]: Failed to get duration async: java.lang.NoClassDefFoundError: javazoom/jl/decoder/Bitstream
java.util.concurrent.CompletionException: java.lang.NoClassDefFoundError: javazoom/jl/decoder/Bitstream
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315) ~[?:?]
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320) ~[?:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1770) ~[?:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760) ~[?:?]
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373) ~[?:?]
	at java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182) ~[?:?]
	at java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655) ~[?:?]
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622) ~[?:?]
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165) ~[?:?]
Caused by: java.lang.NoClassDefFoundError: javazoom/jl/decoder/Bitstream
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getMp3Duration(MusicDurationReader.java:99) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getDurationMillis(MusicDurationReader.java:41) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.lambda$getDurationAsync$0(MusicDurationReader.java:249) ~[main/:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768) ~[?:?]
	... 6 more
Caused by: java.lang.ClassNotFoundException: javazoom.jl.decoder.Bitstream
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getMp3Duration(MusicDurationReader.java:99) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getDurationMillis(MusicDurationReader.java:41) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.lambda$getDurationAsync$0(MusicDurationReader.java:249) ~[main/:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768) ~[?:?]
	... 6 more
[297月2025 19:03:55.449] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] [Kyokuerabu] ♪ 开始播放: qhc
[297月2025 19:03:55.450] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] [Kyokuerabu] ♪ 开始播放: qhc
[297月2025 19:03:57.518] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Saving and pausing game...
[297月2025 19:03:57.527] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:overworld
[297月2025 19:03:57.558] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_nether
[297月2025 19:03:57.559] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_end
